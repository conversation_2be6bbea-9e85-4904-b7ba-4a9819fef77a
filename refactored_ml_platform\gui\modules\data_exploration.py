#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索GUI模块
提供完整的数据探索界面，包括分组概率分析、相关性分析等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
from pathlib import Path
import threading
from typing import List, Dict, Any, Optional
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import logging

try:
    from ...utils.data_explorer import get_data_explorer
    from ...utils.data_loader import get_data_loader
    from ...utils.error_handler import get_error_handler
    from ..core.event_manager import get_event_manager
    from ..components.data_table import DataTableWidget
    from ..components.progress_widget import ProgressWidget
except ImportError:
    # 处理相对导入问题
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))

    from utils.data_explorer import get_data_explorer
    from utils.data_loader import get_data_loader
    from utils.error_handler import get_error_handler
    from gui.core.event_manager import get_event_manager
    from gui.components.data_table import DataTableWidget
    from gui.components.progress_widget import ProgressWidget


class DataExplorationModule:
    """数据探索模块"""
    
    def __init__(self, parent):
        """初始化数据探索模块"""
        self.parent = parent
        self.logger = logging.getLogger(__name__)
        self.data_explorer = get_data_explorer()
        self.data_loader = get_data_loader()
        self.error_handler = get_error_handler()
        self.event_manager = get_event_manager()
        
        # 数据相关
        self.current_df = None
        self.target_column = None
        self.numeric_columns = []
        self.categorical_columns = []
        
        # GUI组件
        self.main_frame = None
        self.canvas_frame = None
        self.current_figure = None
        self.canvas = None
        self.toolbar = None
        
        # 分析结果
        self.analysis_results = {}
        
        self.logger.info("数据探索模块初始化完成")
    
    def create_interface(self, parent_notebook):
        """创建数据探索界面"""
        # 创建主选项卡
        self.main_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.main_frame, text="🔍 数据探索")
        
        # 创建左右分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        # 右侧结果显示面板
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)
        
        # 创建左侧控制界面
        self._create_control_panel(left_frame)
        
        # 创建右侧结果显示界面
        self._create_result_panel(right_frame)
        
        return self.main_frame
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 数据加载区域
        data_frame = ttk.LabelFrame(parent, text="📊 数据加载")
        data_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 数据源选择
        ttk.Label(data_frame, text="数据源:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.data_source_var = tk.StringVar(value="current")
        ttk.Radiobutton(data_frame, text="使用当前数据", 
                       variable=self.data_source_var, value="current").grid(
                           row=0, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Radiobutton(data_frame, text="加载新数据", 
                       variable=self.data_source_var, value="new").grid(
                           row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 文件选择
        file_frame = ttk.Frame(data_frame)
        file_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        self.file_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览", command=self._browse_file).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 加载按钮
        ttk.Button(data_frame, text="🔄 加载数据", 
                  command=self._load_data).grid(row=3, column=0, columnspan=2, pady=10)
        
        data_frame.columnconfigure(1, weight=1)
        
        # 数据信息显示
        info_frame = ttk.LabelFrame(parent, text="📋 数据信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.info_text = tk.Text(info_frame, height=6, wrap=tk.WORD, font=('Consolas', 9))
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 分析配置区域
        config_frame = ttk.LabelFrame(parent, text="⚙️ 分析配置")
        config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 目标变量选择
        ttk.Label(config_frame, text="目标变量:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_var = tk.StringVar()
        self.target_combo = ttk.Combobox(config_frame, textvariable=self.target_var, state="readonly")
        self.target_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        
        # 分析变量选择
        ttk.Label(config_frame, text="分析变量:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        # 变量选择框架
        var_frame = ttk.Frame(config_frame)
        var_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        # 变量列表
        self.var_listbox = tk.Listbox(var_frame, selectmode=tk.MULTIPLE, height=6)
        var_scrollbar = ttk.Scrollbar(var_frame, orient=tk.VERTICAL, command=self.var_listbox.yview)
        self.var_listbox.configure(yscrollcommand=var_scrollbar.set)
        
        self.var_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        var_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 选择按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=5)
        
        ttk.Button(button_frame, text="全选", command=self._select_all_vars).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="清空", command=self._clear_var_selection).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="仅数值", command=self._select_numeric_vars).pack(side=tk.LEFT, padx=2)
        
        config_frame.columnconfigure(1, weight=1)
        
        # 分析参数
        param_frame = ttk.LabelFrame(parent, text="📊 分析参数")
        param_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 分箱数量
        ttk.Label(param_frame, text="分箱数量:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.n_bins_var = tk.IntVar(value=5)
        ttk.Spinbox(param_frame, from_=3, to=10, textvariable=self.n_bins_var, width=10).grid(
            row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 分箱方法
        ttk.Label(param_frame, text="分箱方法:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.binning_method_var = tk.StringVar(value="qcut")
        method_combo = ttk.Combobox(param_frame, textvariable=self.binning_method_var, 
                                   values=["qcut", "cut", "tree"], state="readonly", width=10)
        method_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 最小样本数
        ttk.Label(param_frame, text="最小样本数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.min_samples_var = tk.IntVar(value=20)
        ttk.Spinbox(param_frame, from_=5, to=100, textvariable=self.min_samples_var, width=10).grid(
            row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 分析按钮
        analysis_frame = ttk.LabelFrame(parent, text="🚀 执行分析")
        analysis_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(analysis_frame, text="📈 分组概率分析", 
                  command=self._run_probability_analysis).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(analysis_frame, text="🔗 相关性分析", 
                  command=self._run_correlation_analysis).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(analysis_frame, text="📊 描述性统计", 
                  command=self._run_descriptive_stats).pack(fill=tk.X, padx=5, pady=2)
        
        # 进度条
        self.progress_widget = ProgressWidget(parent)
        self.progress_widget.pack(fill=tk.X, padx=5, pady=5)
    
    def _create_result_panel(self, parent):
        """创建结果显示面板"""
        # 创建notebook用于显示不同类型的结果
        self.result_notebook = ttk.Notebook(parent)
        self.result_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 图表显示选项卡
        self._create_chart_tab()
        
        # 数据表显示选项卡
        self._create_table_tab()
        
        # 统计结果选项卡
        self._create_stats_tab()
    
    def _create_chart_tab(self):
        """创建图表显示选项卡"""
        chart_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(chart_frame, text="📈 图表")
        
        # 图表控制工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar_frame, text="💾 保存图表", 
                  command=self._save_current_chart).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🔄 刷新", 
                  command=self._refresh_chart).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="🗑️ 清空", 
                  command=self._clear_chart).pack(side=tk.LEFT, padx=2)
        
        # 图表显示区域
        self.canvas_frame = ttk.Frame(chart_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_table_tab(self):
        """创建数据表显示选项卡"""
        table_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(table_frame, text="📋 数据表")
        
        # 创建数据表组件
        self.data_table = DataTableWidget(table_frame)
        self.data_table.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_stats_tab(self):
        """创建统计结果选项卡"""
        stats_frame = ttk.Frame(self.result_notebook)
        self.result_notebook.add(stats_frame, text="📊 统计")
        
        # 统计结果显示
        self.stats_text = tk.Text(stats_frame, wrap=tk.WORD, font=('Consolas', 10))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def _browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.file_path_var.set(file_path)

    def _load_data(self):
        """加载数据"""
        def load_task():
            try:
                self.progress_widget.start("正在加载数据...")

                if self.data_source_var.get() == "current":
                    # 使用当前数据（从事件管理器获取）
                    data_info = self.event_manager.get_data('current_data')
                    if data_info and 'dataframe' in data_info:
                        self.current_df = data_info['dataframe']
                    else:
                        raise ValueError("没有找到当前数据，请先在数据管理模块加载数据")
                else:
                    # 加载新数据
                    file_path = self.file_path_var.get().strip()
                    if not file_path:
                        raise ValueError("请选择数据文件")

                    self.current_df = self.data_loader.load_data(file_path)

                if self.current_df is None or self.current_df.empty:
                    raise ValueError("数据加载失败或数据为空")

                # 分析数据基本信息
                self._analyze_data_info()

                # 更新界面
                self.parent.after(0, self._update_data_info_display)
                self.parent.after(0, self._update_variable_lists)

                self.progress_widget.complete("数据加载完成")

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"数据加载失败: {error_msg}")
                self.progress_widget.error(f"数据加载失败: {error_msg}")
                self.parent.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"数据加载失败: {msg}"))

        # 在后台线程中执行
        threading.Thread(target=load_task, daemon=True).start()

    def _analyze_data_info(self):
        """分析数据基本信息"""
        if self.current_df is None:
            return

        # 获取基本信息
        self.data_info = self.data_explorer.analyze_basic_info(self.current_df)

        # 分类数值列和分类列
        self.numeric_columns = self.data_info['numeric_columns']
        self.categorical_columns = self.data_info['categorical_columns']

        self.logger.info(f"数据分析完成: {self.data_info['shape'][0]}行, {self.data_info['shape'][1]}列")

    def _update_data_info_display(self):
        """更新数据信息显示"""
        if not hasattr(self, 'data_info'):
            return

        info_text = f"""数据形状: {self.data_info['shape'][0]} 行 × {self.data_info['shape'][1]} 列
内存使用: {self.data_info['memory_usage']:.2f} MB
重复行数: {self.data_info['duplicates']}
数值列数: {len(self.data_info['numeric_columns'])}
分类列数: {len(self.data_info['categorical_columns'])}

缺失值统计:
"""

        # 添加缺失值信息
        missing_info = []
        for col, missing_count in self.data_info['missing_values'].items():
            if missing_count > 0:
                missing_pct = self.data_info['missing_percentage'][col]
                missing_info.append(f"  {col}: {missing_count} ({missing_pct:.1f}%)")

        if missing_info:
            info_text += "\n".join(missing_info)
        else:
            info_text += "  无缺失值"

        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_text)

    def _update_variable_lists(self):
        """更新变量列表"""
        if self.current_df is None:
            return

        # 更新目标变量下拉框
        columns = list(self.current_df.columns)
        self.target_combo['values'] = columns
        if columns:
            self.target_combo.set(columns[-1])  # 默认选择最后一列作为目标变量

        # 更新分析变量列表框
        self.var_listbox.delete(0, tk.END)
        for col in columns:
            self.var_listbox.insert(tk.END, col)

    def _select_all_vars(self):
        """选择所有变量"""
        self.var_listbox.select_set(0, tk.END)

    def _clear_var_selection(self):
        """清空变量选择"""
        self.var_listbox.selection_clear(0, tk.END)

    def _select_numeric_vars(self):
        """选择数值变量"""
        self.var_listbox.selection_clear(0, tk.END)
        for i, col in enumerate(self.current_df.columns):
            if col in self.numeric_columns:
                self.var_listbox.select_set(i)

    def _get_selected_variables(self) -> List[str]:
        """获取选中的变量"""
        selected_indices = self.var_listbox.curselection()
        selected_vars = []
        for i in selected_indices:
            selected_vars.append(self.var_listbox.get(i))
        return selected_vars

    def _run_probability_analysis(self):
        """运行分组概率分析"""
        if self.current_df is None:
            messagebox.showwarning("警告", "请先加载数据")
            return

        target_var = self.target_var.get()
        if not target_var:
            messagebox.showwarning("警告", "请选择目标变量")
            return

        selected_vars = self._get_selected_variables()
        if not selected_vars:
            messagebox.showwarning("警告", "请选择要分析的变量")
            return

        # 过滤掉目标变量和非数值变量
        continuous_vars = [var for var in selected_vars
                          if var != target_var and var in self.numeric_columns]

        if not continuous_vars:
            messagebox.showwarning("警告", "没有可分析的数值变量")
            return

        def analysis_task():
            try:
                self.progress_widget.start(f"正在分析 {len(continuous_vars)} 个变量...")

                # 执行分组概率分析
                results = self.data_explorer.create_binned_probability_analysis(
                    df=self.current_df,
                    continuous_vars=continuous_vars,
                    target_var=target_var,
                    n_bins=self.n_bins_var.get(),
                    binning_method=self.binning_method_var.get(),
                    min_bin_size=self.min_samples_var.get()
                )

                if not results:
                    raise ValueError("分析未产生有效结果")

                # 保存结果
                self.analysis_results['probability'] = results

                # 更新界面显示
                self.parent.after(0, lambda: self._display_probability_results(results))

                self.progress_widget.complete(f"分组概率分析完成，成功分析 {len(results)} 个变量")

            except Exception as e:
                self.logger.error(f"分组概率分析失败: {e}")
                self.progress_widget.error(f"分析失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"分析失败: {e}"))

        # 在后台线程中执行
        threading.Thread(target=analysis_task, daemon=True).start()

    def _display_probability_results(self, results: Dict[str, Any]):
        """显示分组概率分析结果"""
        try:
            # 显示第一个变量的图表
            if results:
                first_var = list(results.keys())[0]
                self._show_probability_chart(results[first_var])

                # 显示统计摘要
                self._show_probability_stats(results)

                # 创建结果数据表
                self._create_probability_table(results)

        except Exception as e:
            self.logger.error(f"显示概率分析结果失败: {e}")
            messagebox.showerror("错误", f"显示结果失败: {e}")

    def _show_probability_chart(self, analysis_result: Dict[str, Any]):
        """显示概率分析图表"""
        try:
            # 清除之前的图表
            self._clear_chart()

            # 创建概率柱状图
            fig = self.data_explorer.create_probability_bar_chart(analysis_result)

            if fig:
                self.current_figure = fig

                # 在GUI中显示图表
                self.canvas = FigureCanvasTkAgg(fig, self.canvas_frame)
                self.canvas.draw()
                self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

                # 添加工具栏
                self.toolbar = NavigationToolbar2Tk(self.canvas, self.canvas_frame)
                self.toolbar.update()

        except Exception as e:
            self.logger.error(f"显示概率图表失败: {e}")

    def _show_probability_stats(self, results: Dict[str, Any]):
        """显示概率分析统计信息"""
        stats_text = "=== 分组概率分析结果 ===\n\n"

        for var_name, result in results.items():
            stats_text += f"变量: {var_name}\n"
            stats_text += f"目标变量: {result['target']}\n"
            stats_text += f"分箱方法: {result['binning_method']}\n"
            stats_text += f"分箱数量: {result['n_bins']}\n"
            stats_text += f"总样本数: {result['total_samples']}\n\n"

            stats_text += "各组统计:\n"
            stats_text += f"{'组别':<20} {'样本数':<8} {'概率':<8} {'置信区间':<20}\n"
            stats_text += "-" * 60 + "\n"

            for group_result in result['results']:
                interval_str = group_result['interval_str']
                count = group_result['count']
                prob = group_result['probability']
                ci_lower = group_result['ci_lower']
                ci_upper = group_result['ci_upper']

                stats_text += f"{interval_str:<20} {count:<8} {prob:<8.3f} [{ci_lower:.3f}, {ci_upper:.3f}]\n"

            stats_text += "\n" + "="*60 + "\n\n"

        # 显示统计信息
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def _create_probability_table(self, results: Dict[str, Any]):
        """创建概率分析结果表格"""
        try:
            # 准备表格数据
            table_data = []
            columns = ['变量', '组别', '区间', '样本数', '正例数', '概率', '置信区间下限', '置信区间上限', '均值', '标准差']

            for var_name, result in results.items():
                for group_result in result['results']:
                    row = [
                        var_name,
                        str(group_result['group']),
                        group_result['interval_str'],
                        group_result['count'],
                        group_result['positive_count'],
                        f"{group_result['probability']:.4f}",
                        f"{group_result['ci_lower']:.4f}",
                        f"{group_result['ci_upper']:.4f}",
                        f"{group_result['mean_value']:.4f}",
                        f"{group_result['std_value']:.4f}"
                    ]
                    table_data.append(row)

            # 更新数据表
            if table_data:
                df_result = pd.DataFrame(table_data, columns=columns)
                self.data_table.update_data(df_result)

        except Exception as e:
            self.logger.error(f"创建概率分析表格失败: {e}")

    def _run_correlation_analysis(self):
        """运行相关性分析"""
        if self.current_df is None:
            messagebox.showwarning("警告", "请先加载数据")
            return

        def analysis_task():
            try:
                self.progress_widget.start("正在进行相关性分析...")

                # 执行相关性分析
                correlation_matrix = self.data_explorer.analyze_correlation(self.current_df)

                if correlation_matrix.empty:
                    raise ValueError("没有足够的数值变量进行相关性分析")

                # 保存结果
                self.analysis_results['correlation'] = correlation_matrix

                # 更新界面显示
                self.parent.after(0, lambda: self._display_correlation_results(correlation_matrix))

                self.progress_widget.complete("相关性分析完成")

            except Exception as e:
                self.logger.error(f"相关性分析失败: {e}")
                self.progress_widget.error(f"分析失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"分析失败: {e}"))

        # 在后台线程中执行
        threading.Thread(target=analysis_task, daemon=True).start()

    def _display_correlation_results(self, correlation_matrix: pd.DataFrame):
        """显示相关性分析结果"""
        try:
            # 清除之前的图表
            self._clear_chart()

            # 创建相关性热力图
            fig = self.data_explorer.create_correlation_heatmap(correlation_matrix)

            if fig:
                self.current_figure = fig

                # 在GUI中显示图表
                self.canvas = FigureCanvasTkAgg(fig, self.canvas_frame)
                self.canvas.draw()
                self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

                # 添加工具栏
                self.toolbar = NavigationToolbar2Tk(self.canvas, self.canvas_frame)
                self.toolbar.update()

            # 显示相关性矩阵数据表
            self.data_table.update_data(correlation_matrix)

            # 显示统计信息
            self._show_correlation_stats(correlation_matrix)

        except Exception as e:
            self.logger.error(f"显示相关性结果失败: {e}")
            messagebox.showerror("错误", f"显示结果失败: {e}")

    def _show_correlation_stats(self, correlation_matrix: pd.DataFrame):
        """显示相关性统计信息"""
        stats_text = "=== 相关性分析结果 ===\n\n"

        # 找出高相关性的变量对
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.7:  # 高相关性阈值
                    high_corr_pairs.append((
                        correlation_matrix.columns[i],
                        correlation_matrix.columns[j],
                        corr_value
                    ))

        stats_text += f"变量数量: {len(correlation_matrix.columns)}\n"
        stats_text += f"高相关性变量对 (|r| > 0.7): {len(high_corr_pairs)}\n\n"

        if high_corr_pairs:
            stats_text += "高相关性变量对:\n"
            stats_text += f"{'变量1':<20} {'变量2':<20} {'相关系数':<10}\n"
            stats_text += "-" * 50 + "\n"

            for var1, var2, corr in sorted(high_corr_pairs, key=lambda x: abs(x[2]), reverse=True):
                stats_text += f"{var1:<20} {var2:<20} {corr:<10.4f}\n"
        else:
            stats_text += "没有发现高相关性变量对\n"

        # 显示统计信息
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def _run_descriptive_stats(self):
        """运行描述性统计分析"""
        if self.current_df is None:
            messagebox.showwarning("警告", "请先加载数据")
            return

        def analysis_task():
            try:
                self.progress_widget.start("正在进行描述性统计分析...")

                # 执行描述性统计分析
                stats_results = self.data_explorer.analyze_descriptive_stats(self.current_df)

                # 保存结果
                self.analysis_results['descriptive'] = stats_results

                # 更新界面显示
                self.parent.after(0, lambda: self._display_descriptive_results(stats_results))

                self.progress_widget.complete("描述性统计分析完成")

            except Exception as e:
                self.logger.error(f"描述性统计分析失败: {e}")
                self.progress_widget.error(f"分析失败: {e}")
                self.parent.after(0, lambda: messagebox.showerror("错误", f"分析失败: {e}"))

        # 在后台线程中执行
        threading.Thread(target=analysis_task, daemon=True).start()

    def _display_descriptive_results(self, stats_results: Dict[str, pd.DataFrame]):
        """显示描述性统计结果"""
        try:
            # 清除图表
            self._clear_chart()

            # 显示数值变量统计
            if 'numeric' in stats_results:
                self.data_table.update_data(stats_results['numeric'])

            # 显示统计摘要
            self._show_descriptive_stats(stats_results)

        except Exception as e:
            self.logger.error(f"显示描述性统计结果失败: {e}")
            messagebox.showerror("错误", f"显示结果失败: {e}")

    def _show_descriptive_stats(self, stats_results: Dict[str, pd.DataFrame]):
        """显示描述性统计信息"""
        stats_text = "=== 描述性统计分析结果 ===\n\n"

        # 数值变量统计
        if 'numeric' in stats_results:
            numeric_stats = stats_results['numeric']
            stats_text += f"数值变量统计 ({len(numeric_stats.columns)} 个变量):\n"
            stats_text += f"{'统计量':<12} {'最小值':<10} {'最大值':<10} {'均值':<10} {'标准差':<10}\n"
            stats_text += "-" * 60 + "\n"

            for col in numeric_stats.columns:
                min_val = numeric_stats.loc['min', col]
                max_val = numeric_stats.loc['max', col]
                mean_val = numeric_stats.loc['mean', col]
                std_val = numeric_stats.loc['std', col]

                stats_text += f"{col:<12} {min_val:<10.3f} {max_val:<10.3f} {mean_val:<10.3f} {std_val:<10.3f}\n"

            stats_text += "\n"

        # 分类变量统计
        if 'categorical' in stats_results:
            cat_stats = stats_results['categorical']
            stats_text += f"分类变量统计 ({len(cat_stats)} 个变量):\n"
            stats_text += f"{'变量名':<15} {'唯一值数':<10} {'最频繁值':<15} {'缺失数':<10}\n"
            stats_text += "-" * 60 + "\n"

            for _, row in cat_stats.iterrows():
                stats_text += f"{row['column']:<15} {row['unique_count']:<10} {str(row['most_frequent']):<15} {row['missing_count']:<10}\n"

        # 显示统计信息
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def _save_current_chart(self):
        """保存当前图表"""
        if self.current_figure is None:
            messagebox.showwarning("警告", "没有可保存的图表")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("PDF files", "*.pdf"),
                ("SVG files", "*.svg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                self.current_figure.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {file_path}")
                self.logger.info(f"图表已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存图表失败: {e}")
                self.logger.error(f"保存图表失败: {e}")

    def _refresh_chart(self):
        """刷新图表"""
        if self.canvas:
            self.canvas.draw()

    def _clear_chart(self):
        """清空图表"""
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
            self.canvas = None

        if self.toolbar:
            self.toolbar.destroy()
            self.toolbar = None

        if self.current_figure:
            plt.close(self.current_figure)
            self.current_figure = None

    def get_analysis_results(self) -> Dict[str, Any]:
        """获取分析结果"""
        return self.analysis_results.copy()

    def clear_results(self):
        """清空所有结果"""
        self.analysis_results.clear()
        self._clear_chart()
        self.data_table.clear()
        self.stats_text.delete(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
