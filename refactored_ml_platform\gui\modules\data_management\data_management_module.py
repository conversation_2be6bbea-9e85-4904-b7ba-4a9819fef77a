#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理主模块
整合数据加载、预览、验证、预处理等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any, Tuple
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_classif
import re
import logging

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes
from ...components.data_widgets import FileSelector, DataPreviewWidget
from ...components.progress_widgets import ProgressWidget, StatusIndicator

# 导入算法模块
try:
    from algorithms.data_preprocessing import load_and_clean_data, detect_target_column
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    from algorithms.data_preprocessing import load_and_clean_data, detect_target_column


class DataManagementModule(BaseGUI):
    """
    数据管理模块主类
    提供完整的数据管理功能界面
    """
    
    def __init__(self, parent: tk.Widget):
        """初始化数据管理模块"""
        self.current_data: Optional[pd.DataFrame] = None
        self.original_data: Optional[pd.DataFrame] = None  # 保存原始数据
        self.data_file_path: Optional[str] = None
        self.target_column: Optional[str] = None
        self.preprocessor_state: Dict[str, Any] = {}  # 保存预处理器状态

        super().__init__(parent)

        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页容器
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.register_component('notebook', self.notebook)
        
        # 数据加载标签页
        self._create_data_loader_tab()
        
        # 数据预览标签页
        self._create_data_preview_tab()
        
        # 数据验证标签页
        self._create_data_validation_tab()
        
        # 数据预处理标签页
        self._create_data_preprocessing_tab()
    
    def _create_data_loader_tab(self):
        """创建数据加载标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        loader_frame = factory.create_frame(self.notebook)
        self.notebook.add(loader_frame, text="数据加载")
        self.register_component('loader_frame', loader_frame)
        
        # 标题
        title_frame = factory.create_frame(loader_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        
        factory.create_label(title_frame, text="数据文件加载", style='title').pack(anchor=tk.W)
        factory.create_label(title_frame, text="支持CSV格式文件，目标变量应命名为'label'或'target'", 
                           style='secondary').pack(anchor=tk.W, pady=(5, 0))
        
        # 文件选择器
        self.file_selector = FileSelector(
            loader_frame,
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            on_file_selected=self._on_file_selected
        )
        
        # 状态指示器
        status_frame = factory.create_frame(loader_frame)
        status_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        
        self.status_indicator = StatusIndicator(status_frame)
        
        # 进度条
        self.progress_widget = ProgressWidget(loader_frame, show_percentage=True)
        
        # 加载按钮
        button_frame = factory.create_frame(loader_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.load_button = factory.create_button(
            button_frame, 
            text="加载数据", 
            command=self._load_data,
            style='primary'
        )
        self.load_button.pack(side=tk.LEFT)
        self.load_button.config(state=tk.DISABLED)
        self.register_component('load_button', self.load_button)
        
        # 重新加载按钮
        reload_button = factory.create_button(
            button_frame,
            text="重新加载",
            command=self._reload_data,
            style='secondary'
        )
        reload_button.pack(side=tk.LEFT, padx=(10, 0))
        reload_button.config(state=tk.DISABLED)
        self.register_component('reload_button', reload_button)
    
    def _create_data_preview_tab(self):
        """创建数据预览标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        preview_frame = factory.create_frame(self.notebook)
        self.notebook.add(preview_frame, text="数据预览")
        self.register_component('preview_frame', preview_frame)
        
        # 数据预览组件
        self.data_preview = DataPreviewWidget(preview_frame)
    
    def _create_data_validation_tab(self):
        """创建数据验证标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        validation_frame = factory.create_frame(self.notebook)
        self.notebook.add(validation_frame, text="数据验证")
        self.register_component('validation_frame', validation_frame)
        
        # 标题
        title_frame = factory.create_frame(validation_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        
        factory.create_label(title_frame, text="数据质量验证", style='title').pack(anchor=tk.W)
        
        # 验证结果显示区域
        result_frame = factory.create_frame(validation_frame, style='card')
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # 验证结果文本框
        self.validation_text = factory.create_text(result_frame, height=20, state=tk.DISABLED)
        self.validation_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.register_component('validation_text', self.validation_text)
        
        # 验证按钮
        validate_button = factory.create_button(
            validation_frame,
            text="开始验证",
            command=self._validate_data,
            style='primary'
        )
        validate_button.pack(pady=(0, 10))
        validate_button.config(state=tk.DISABLED)
        self.register_component('validate_button', validate_button)
    
    def _create_data_preprocessing_tab(self):
        """创建数据预处理标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        preprocessing_frame = factory.create_frame(self.notebook)
        self.notebook.add(preprocessing_frame, text="数据预处理")
        self.register_component('preprocessing_frame', preprocessing_frame)
        
        # 标题
        title_frame = factory.create_frame(preprocessing_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        
        factory.create_label(title_frame, text="数据预处理配置", style='title').pack(anchor=tk.W)
        
        # 预处理选项
        options_frame = factory.create_frame(preprocessing_frame, style='card')
        options_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # 创建选项卡
        options_notebook = factory.create_notebook(options_frame)
        options_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 基础预处理选项卡
        basic_frame = factory.create_frame(options_notebook)
        options_notebook.add(basic_frame, text="基础处理")

        # 缺失值处理
        missing_frame = factory.create_frame(basic_frame)
        missing_frame.pack(fill=tk.X, padx=10, pady=10)

        factory.create_label(missing_frame, text="缺失值处理:", style='default').pack(anchor=tk.W)

        self.missing_method_var = tk.StringVar(value="drop")
        self.register_variable('missing_method', self.missing_method_var)

        missing_options = [
            ("删除含缺失值的行", "drop"),
            ("均值填充 (数值列)", "mean"),
            ("中位数填充 (数值列)", "median"),
            ("众数填充 (所有列)", "mode"),
            ("前向填充", "ffill"),
            ("后向填充", "bfill")
        ]

        for text, value in missing_options:
            radio = factory.create_radiobutton(missing_frame, text=text,
                                             variable=self.missing_method_var, value=value)
            radio.pack(anchor=tk.W, padx=20)

        # 特征缩放
        scaling_frame = factory.create_frame(basic_frame)
        scaling_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        factory.create_label(scaling_frame, text="特征缩放:", style='default').pack(anchor=tk.W)

        self.scaling_method_var = tk.StringVar(value="standard")
        self.register_variable('scaling_method', self.scaling_method_var)

        scaling_options = [
            ("标准化 (StandardScaler)", "standard"),
            ("最小-最大缩放 (MinMaxScaler)", "minmax"),
            ("鲁棒缩放 (RobustScaler)", "robust"),
            ("不进行缩放", "none")
        ]

        for text, value in scaling_options:
            radio = factory.create_radiobutton(scaling_frame, text=text,
                                             variable=self.scaling_method_var, value=value)
            radio.pack(anchor=tk.W, padx=20)

        # 高级预处理选项卡
        advanced_frame = factory.create_frame(options_notebook)
        options_notebook.add(advanced_frame, text="高级处理")

        # 数据分割设置
        split_frame = factory.create_frame(advanced_frame)
        split_frame.pack(fill=tk.X, padx=10, pady=10)

        factory.create_label(split_frame, text="数据分割设置:", style='default').pack(anchor=tk.W)

        split_config_frame = factory.create_frame(split_frame)
        split_config_frame.pack(fill=tk.X, padx=20, pady=5)

        factory.create_label(split_config_frame, text="测试集比例:").pack(side=tk.LEFT)
        self.test_size_var = tk.DoubleVar(value=0.2)
        self.register_variable('test_size', self.test_size_var)
        test_size_scale = factory.create_scale(split_config_frame, from_=0.1, to=0.5,
                                             resolution=0.05, orient=tk.HORIZONTAL,
                                             variable=self.test_size_var)
        test_size_scale.pack(side=tk.LEFT, padx=10)

        factory.create_label(split_config_frame, text="随机种子:").pack(side=tk.LEFT, padx=(20, 0))
        self.random_seed_var = tk.IntVar(value=42)
        self.register_variable('random_seed', self.random_seed_var)
        seed_entry = factory.create_entry(split_config_frame, textvariable=self.random_seed_var, width=10)
        seed_entry.pack(side=tk.LEFT, padx=5)

        # 特征选择
        feature_frame = factory.create_frame(advanced_frame)
        feature_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        self.enable_feature_selection = tk.BooleanVar(value=False)
        self.register_variable('enable_feature_selection', self.enable_feature_selection)

        feature_cb = factory.create_checkbutton(feature_frame, text="启用特征选择",
                                               variable=self.enable_feature_selection,
                                               command=self._toggle_feature_selection)
        feature_cb.pack(anchor=tk.W)

        self.feature_selection_frame = factory.create_frame(feature_frame)
        self.feature_selection_frame.pack(fill=tk.X, padx=20, pady=5)

        factory.create_label(self.feature_selection_frame, text="选择特征数量:").pack(side=tk.LEFT)
        self.feature_k_var = tk.IntVar(value=10)
        self.register_variable('feature_k', self.feature_k_var)
        feature_k_scale = factory.create_scale(self.feature_selection_frame, from_=5, to=50,
                                             orient=tk.HORIZONTAL, variable=self.feature_k_var)
        feature_k_scale.pack(side=tk.LEFT, padx=10)

        # 初始状态下禁用特征选择选项
        for widget in self.feature_selection_frame.winfo_children():
            widget.config(state=tk.DISABLED)
        
        # 预处理按钮
        button_frame = factory.create_frame(preprocessing_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        preprocess_button = factory.create_button(
            button_frame,
            text="🔧 应用预处理",
            command=self._apply_preprocessing,
            style='primary'
        )
        preprocess_button.pack(side=tk.LEFT)
        preprocess_button.config(state=tk.DISABLED)
        self.register_component('preprocess_button', preprocess_button)

        reset_button = factory.create_button(
            button_frame,
            text="🔄 重置数据",
            command=self._reset_data,
            style='secondary'
        )
        reset_button.pack(side=tk.LEFT, padx=(10, 0))
        reset_button.config(state=tk.DISABLED)
        self.register_component('reset_button', reset_button)
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据加载完成事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
    
    def _on_file_selected(self, file_path: str):
        """文件选择回调"""
        self.data_file_path = file_path
        self.load_button.config(state=tk.NORMAL)
        self.status_indicator.set_status("idle", f"已选择文件: {file_path}")
    
    def _load_data(self):
        """加载数据"""
        if not self.data_file_path:
            self.show_error("错误", "请先选择数据文件")
            return

        try:
            self.status_indicator.set_status("running", "正在加载数据...")
            self.progress_widget.set_progress(10, "读取文件...")

            # 使用增强的数据加载功能
            self.current_data, self.target_column = load_and_clean_data(self.data_file_path)
            self.progress_widget.set_progress(40, "清理数据...")

            # 保存原始数据副本
            self.original_data = self.current_data.copy()

            # 基本验证
            if self.current_data.empty:
                raise ValueError("数据文件为空")

            self.progress_widget.set_progress(60, "检测目标变量...")

            # 检测目标列
            if not self.target_column:
                self.target_column = detect_target_column(self.current_data)

            self.progress_widget.set_progress(80, "更新界面...")

            # 更新预览
            self.data_preview.load_data(self.current_data)

            # 启用其他功能按钮
            self._enable_data_dependent_buttons()

            self.progress_widget.complete("数据加载完成")
            self.status_indicator.set_status("success",
                                           f"成功加载 {len(self.current_data)} 行 {len(self.current_data.columns)} 列 | 目标列: {self.target_column}")

            # 存储数据到事件管理器
            self.event_manager.set_data('current_data', {
                'dataframe': self.current_data,
                'file_path': self.data_file_path,
                'target_column': self.target_column,
                'data_shape': self.current_data.shape,
                'columns': list(self.current_data.columns)
            })

            # 发布事件
            self.publish_event(EventTypes.DATA_LOADED, {
                'data': self.current_data,
                'file_path': self.data_file_path,
                'target_column': self.target_column,
                'data_shape': self.current_data.shape,
                'columns': list(self.current_data.columns)
            })

        except Exception as e:
            self.progress_widget.reset()
            self.status_indicator.set_status("error", f"加载失败: {str(e)}")
            self.show_error("加载失败", f"无法加载数据文件:\n{str(e)}")
            self.logger.error(f"数据加载失败: {e}")
    
    def _reload_data(self):
        """重新加载数据"""
        if self.data_file_path:
            self._load_data()

    def _toggle_feature_selection(self):
        """切换特征选择选项的启用状态"""
        enabled = self.enable_feature_selection.get()
        state = tk.NORMAL if enabled else tk.DISABLED

        for widget in self.feature_selection_frame.winfo_children():
            widget.config(state=state)

    def _reset_data(self):
        """重置数据到原始状态"""
        if self.original_data is not None:
            self.current_data = self.original_data.copy()
            self.data_preview.load_data(self.current_data)
            self.status_indicator.set_status("success", "数据已重置到原始状态")
            self.logger.info("数据已重置到原始状态")

            # 更新事件管理器中的数据
            self.event_manager.set_data('current_data', {
                'dataframe': self.current_data,
                'file_path': self.data_file_path,
                'target_column': self.target_column,
                'data_shape': self.current_data.shape,
                'columns': list(self.current_data.columns)
            })

            # 发布数据重置事件
            self.publish_event(EventTypes.DATA_LOADED, {
                'data': self.current_data,
                'file_path': self.data_file_path,
                'target_column': self.target_column,
                'data_shape': self.current_data.shape,
                'columns': list(self.current_data.columns),
                'reset': True
            })
        else:
            self.show_warning("警告", "没有原始数据可以重置")
    
    def _validate_data(self):
        """验证数据"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return

        try:
            validation_results = []
            issues = []

            # 基本信息
            validation_results.append("=== 数据基本信息 ===")
            validation_results.append(f"数据形状: {self.current_data.shape}")
            validation_results.append(f"内存使用: {self.current_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
            validation_results.append(f"数值列数: {len(self.current_data.select_dtypes(include=[np.number]).columns)}")
            validation_results.append(f"分类列数: {len(self.current_data.select_dtypes(include=['object']).columns)}")
            validation_results.append("")

            # 缺失值检查
            validation_results.append("=== 缺失值检查 ===")
            missing_info = self.current_data.isnull().sum()
            missing_cols = missing_info[missing_info > 0]

            if len(missing_cols) == 0:
                validation_results.append("✓ 无缺失值")
            else:
                validation_results.append("⚠ 发现缺失值:")
                for col, count in missing_cols.items():
                    pct = (count / len(self.current_data)) * 100
                    validation_results.append(f"  {col}: {count} ({pct:.1f}%)")
                    if pct > 50:
                        issues.append(f"列 '{col}' 缺失值过多 ({pct:.1f}%)")

            validation_results.append("")

            # 重复行检查
            validation_results.append("=== 重复行检查 ===")
            duplicate_count = self.current_data.duplicated().sum()
            if duplicate_count == 0:
                validation_results.append("✓ 无重复行")
            else:
                pct = (duplicate_count / len(self.current_data)) * 100
                validation_results.append(f"⚠ 发现 {duplicate_count} 行重复数据 ({pct:.1f}%)")
                if pct > 10:
                    issues.append(f"重复行过多 ({pct:.1f}%)")

            validation_results.append("")

            # 数据类型检查
            validation_results.append("=== 数据类型检查 ===")
            for col in self.current_data.columns:
                dtype = str(self.current_data[col].dtype)
                unique_count = self.current_data[col].nunique()
                validation_results.append(f"{col}: {dtype} (唯一值: {unique_count})")

                # 检查可能的数据类型问题
                if dtype == 'object' and unique_count > len(self.current_data) * 0.8:
                    issues.append(f"列 '{col}' 可能包含过多唯一值，考虑是否为ID列")

            validation_results.append("")

            # 目标变量检查
            validation_results.append("=== 目标变量检查 ===")
            if hasattr(self, 'target_column') and self.target_column:
                target_col = self.target_column
                validation_results.append(f"✓ 目标变量: {target_col}")

                # 检查目标变量分布
                value_counts = self.current_data[target_col].value_counts()
                validation_results.append("目标变量分布:")
                min_class_pct = float('inf')
                for value, count in value_counts.items():
                    pct = (count / len(self.current_data)) * 100
                    validation_results.append(f"  {value}: {count} ({pct:.1f}%)")
                    min_class_pct = min(min_class_pct, pct)

                # 检查类别不平衡
                if min_class_pct < 5:
                    issues.append(f"目标变量存在严重类别不平衡 (最小类别: {min_class_pct:.1f}%)")
                elif min_class_pct < 20:
                    issues.append(f"目标变量存在轻微类别不平衡 (最小类别: {min_class_pct:.1f}%)")
            else:
                validation_results.append("⚠ 未找到目标变量")
                issues.append("未找到目标变量列")

            validation_results.append("")

            # 异常值检查（仅对数值列）
            validation_results.append("=== 异常值检查 ===")
            numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                outlier_cols = []
                for col in numeric_cols:
                    Q1 = self.current_data[col].quantile(0.25)
                    Q3 = self.current_data[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    outliers = ((self.current_data[col] < lower_bound) |
                               (self.current_data[col] > upper_bound)).sum()
                    if outliers > 0:
                        pct = (outliers / len(self.current_data)) * 100
                        validation_results.append(f"  {col}: {outliers} 个异常值 ({pct:.1f}%)")
                        outlier_cols.append(col)
                        if pct > 10:
                            issues.append(f"列 '{col}' 异常值过多 ({pct:.1f}%)")

                if not outlier_cols:
                    validation_results.append("✓ 未发现明显异常值")
            else:
                validation_results.append("无数值列可检查")

            validation_results.append("")

            # 数据质量总结
            validation_results.append("=== 数据质量总结 ===")
            if not issues:
                validation_results.append("✓ 数据质量良好，无重大问题")
                quality_score = 100
            else:
                validation_results.append("⚠ 发现以下问题:")
                for issue in issues:
                    validation_results.append(f"  • {issue}")
                quality_score = max(0, 100 - len(issues) * 15)

            validation_results.append(f"数据质量评分: {quality_score}/100")

            # 显示验证结果
            self.validation_text.config(state=tk.NORMAL)
            self.validation_text.delete(1.0, tk.END)
            self.validation_text.insert(tk.END, "\n".join(validation_results))
            self.validation_text.config(state=tk.DISABLED)

            self.publish_event(EventTypes.DATA_VALIDATED, {
                'data': self.current_data,
                'validation_results': validation_results,
                'issues': issues,
                'quality_score': quality_score
            })

            self.logger.info(f"数据验证完成，质量评分: {quality_score}")

        except Exception as e:
            error_msg = f"数据验证过程中出现错误: {str(e)}"
            self.show_error("验证失败", error_msg)
            self.logger.error(error_msg)
    
    def _apply_preprocessing(self):
        """应用数据预处理"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return

        try:
            self.logger.info("开始数据预处理")
            processed_data = self.current_data.copy()
            preprocessing_steps = []

            # 1. 缺失值处理
            missing_method = self.missing_method_var.get()
            missing_count_before = processed_data.isnull().sum().sum()

            if missing_count_before > 0:
                preprocessing_steps.append(f"处理 {missing_count_before} 个缺失值")

                if missing_method == "drop":
                    processed_data = processed_data.dropna()
                    preprocessing_steps.append("删除含缺失值的行")
                elif missing_method == "mean":
                    numeric_cols = processed_data.select_dtypes(include=[np.number]).columns
                    processed_data[numeric_cols] = processed_data[numeric_cols].fillna(
                        processed_data[numeric_cols].mean())
                    preprocessing_steps.append("数值列使用均值填充")
                elif missing_method == "median":
                    numeric_cols = processed_data.select_dtypes(include=[np.number]).columns
                    processed_data[numeric_cols] = processed_data[numeric_cols].fillna(
                        processed_data[numeric_cols].median())
                    preprocessing_steps.append("数值列使用中位数填充")
                elif missing_method == "mode":
                    for col in processed_data.columns:
                        if processed_data[col].isnull().any():
                            mode_val = processed_data[col].mode()
                            if len(mode_val) > 0:
                                processed_data[col] = processed_data[col].fillna(mode_val[0])
                    preprocessing_steps.append("所有列使用众数填充")
                elif missing_method == "ffill":
                    processed_data = processed_data.fillna(method='ffill')
                    preprocessing_steps.append("使用前向填充")
                elif missing_method == "bfill":
                    processed_data = processed_data.fillna(method='bfill')
                    preprocessing_steps.append("使用后向填充")

            # 2. 分离特征和目标变量
            if self.target_column and self.target_column in processed_data.columns:
                X = processed_data.drop(columns=[self.target_column])
                y = processed_data[self.target_column]
                target_col = self.target_column
            else:
                # 使用最后一列作为目标变量
                X = processed_data.iloc[:, :-1]
                y = processed_data.iloc[:, -1]
                target_col = processed_data.columns[-1]

            preprocessing_steps.append(f"目标变量: {target_col}")
            preprocessing_steps.append(f"特征数量: {X.shape[1]}")

            # 3. 数据分割
            test_size = self.test_size_var.get()
            random_seed = self.random_seed_var.get()

            # 确保每个类别至少有足够的样本
            unique_classes = len(np.unique(y))
            min_samples_per_class = 2
            min_test_samples = unique_classes * min_samples_per_class

            if len(y) * test_size < min_test_samples:
                adjusted_test_size = min(max(min_test_samples / len(y), 0.1), 0.5)
                self.logger.warning(f"数据集较小，调整测试集比例从 {test_size:.2f} 到 {adjusted_test_size:.2f}")
                test_size = adjusted_test_size
                preprocessing_steps.append(f"调整测试集比例到 {test_size:.2f}")

            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_seed, stratify=y
            )

            preprocessing_steps.append(f"训练集: {X_train.shape[0]} 行")
            preprocessing_steps.append(f"测试集: {X_test.shape[0]} 行")

            # 4. 特征缩放
            scaling_method = self.scaling_method_var.get()
            scaler = None

            if scaling_method != "none":
                numeric_cols = X_train.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    if scaling_method == "standard":
                        scaler = StandardScaler()
                        preprocessing_steps.append("应用标准化缩放")
                    elif scaling_method == "minmax":
                        scaler = MinMaxScaler()
                        preprocessing_steps.append("应用最小-最大缩放")
                    elif scaling_method == "robust":
                        scaler = RobustScaler()
                        preprocessing_steps.append("应用鲁棒缩放")

                    if scaler:
                        X_train_scaled = X_train.copy()
                        X_test_scaled = X_test.copy()

                        X_train_scaled[numeric_cols] = scaler.fit_transform(X_train[numeric_cols])
                        X_test_scaled[numeric_cols] = scaler.transform(X_test[numeric_cols])

                        X_train, X_test = X_train_scaled, X_test_scaled

            # 5. 特征选择
            feature_selector = None
            selected_features = list(X.columns)

            if self.enable_feature_selection.get():
                feature_k = min(self.feature_k_var.get(), X_train.shape[1])
                feature_selector = SelectKBest(score_func=f_classif, k=feature_k)

                X_train_selected = feature_selector.fit_transform(X_train, y_train)
                X_test_selected = feature_selector.transform(X_test)

                # 获取选择的特征名称
                selected_indices = feature_selector.get_support(indices=True)
                selected_features = [X.columns[i] for i in selected_indices]

                # 转换回DataFrame
                X_train = pd.DataFrame(X_train_selected, columns=selected_features, index=X_train.index)
                X_test = pd.DataFrame(X_test_selected, columns=selected_features, index=X_test.index)

                preprocessing_steps.append(f"特征选择: 保留 {len(selected_features)} 个特征")

            # 保存预处理器状态
            self.preprocessor_state = {
                'scaler': scaler,
                'feature_selector': feature_selector,
                'selected_features': selected_features,
                'target_column': target_col,
                'preprocessing_config': {
                    'missing_method': missing_method,
                    'scaling_method': scaling_method,
                    'test_size': test_size,
                    'random_seed': random_seed,
                    'feature_selection': self.enable_feature_selection.get(),
                    'feature_k': self.feature_k_var.get() if self.enable_feature_selection.get() else None
                }
            }

            # 更新当前数据显示
            self.current_data = processed_data
            self.data_preview.load_data(self.current_data)

            # 显示预处理结果
            result_msg = "数据预处理完成！\n\n预处理步骤:\n" + "\n".join([f"• {step}" for step in preprocessing_steps])
            self.show_info("预处理完成", result_msg)

            # 发布预处理完成事件
            self.publish_event(EventTypes.DATA_PREPROCESSED, {
                'data': processed_data,
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test,
                'feature_names': selected_features,
                'target_column': target_col,
                'scaler': scaler,
                'feature_selector': feature_selector,
                'preprocessing_config': self.preprocessor_state['preprocessing_config'],
                'preprocessing_steps': preprocessing_steps
            })

            self.logger.info(f"数据预处理完成: {len(preprocessing_steps)} 个步骤")

        except Exception as e:
            error_msg = f"数据预处理过程中出现错误: {str(e)}"
            self.show_error("预处理失败", error_msg)
            self.logger.error(error_msg)
    
    def _enable_data_dependent_buttons(self):
        """启用依赖数据的按钮"""
        buttons = ['reload_button', 'validate_button', 'preprocess_button', 'reset_button']
        for button_name in buttons:
            button = self.get_component(button_name)
            if button:
                button.config(state=tk.NORMAL)
    
    def _on_data_loaded(self, data):
        """数据加载完成事件处理"""
        pass
    
    def _on_data_preprocessed(self, data):
        """数据预处理完成事件处理"""
        pass
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据"""
        return self.current_data
    
    def get_data_file_path(self) -> Optional[str]:
        """获取数据文件路径"""
        return self.data_file_path
