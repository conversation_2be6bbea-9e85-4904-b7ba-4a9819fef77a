2025-08-24 09:57:28 - algorithms.model_training - INFO - 开始训练模型: SVM
2025-08-24 09:57:28 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:28 - algorithms.model_training - INFO - 模型 SVM 训练完成，用时 0.00秒
2025-08-24 09:57:28 - algorithms.model_training - INFO - R²: 0.0405, MSE: 19269.5792
2025-08-24 09:57:28 - algorithms.model_training - INFO - 模型 SVM 的结果已缓存到: D:\Code\MM01U\refactored_ml_platform\cache\SVM_results.joblib
2025-08-24 09:57:28 - algorithms.model_training - INFO - 开始训练模型: Logistic
2025-08-24 09:57:28 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:28 - algorithms.model_training - INFO - 模型 Logistic 训练完成，用时 0.03秒
2025-08-24 09:57:28 - algorithms.model_training - INFO - R²: 1.0000, MSE: 0.0113
2025-08-24 09:57:28 - algorithms.model_training - INFO - 模型 Logistic 的结果已缓存到: D:\Code\MM01U\refactored_ml_platform\cache\Logistic_results.joblib
2025-08-24 09:57:28 - algorithms.model_training - INFO - 开始训练模型: NaiveBayes
2025-08-24 09:57:28 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:28 - algorithms.model_training - INFO - 开始训练模型: NeuralNet
2025-08-24 09:57:28 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:28 - algorithms.model_training - INFO - 模型 NeuralNet 训练完成，用时 0.43秒
2025-08-24 09:57:28 - algorithms.model_training - INFO - R²: 0.9914, MSE: 291.7895
2025-08-24 09:57:28 - algorithms.model_training - INFO - 模型 NeuralNet 的结果已缓存到: D:\Code\MM01U\refactored_ml_platform\cache\NeuralNet_results.joblib
2025-08-24 09:57:32 - algorithms.model_training - INFO - 开始训练模型: SVM
2025-08-24 09:57:32 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:32 - algorithms.model_training - INFO - 模型 SVM 训练完成，用时 0.00秒
2025-08-24 09:57:32 - algorithms.model_training - INFO - R²: 0.0405, MSE: 19269.5792
2025-08-24 09:57:32 - algorithms.model_training - INFO - 模型 SVM 的结果已缓存到: D:\Code\MM01U\refactored_ml_platform\cache\SVM_results.joblib
2025-08-24 09:57:32 - algorithms.model_training - INFO - 开始训练模型: Logistic
2025-08-24 09:57:32 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:32 - algorithms.model_training - INFO - 模型 Logistic 训练完成，用时 0.02秒
2025-08-24 09:57:32 - algorithms.model_training - INFO - R²: 1.0000, MSE: 0.0113
2025-08-24 09:57:32 - algorithms.model_training - INFO - 模型 Logistic 的结果已缓存到: D:\Code\MM01U\refactored_ml_platform\cache\Logistic_results.joblib
2025-08-24 09:57:32 - algorithms.model_training - INFO - 开始训练模型: NaiveBayes
2025-08-24 09:57:32 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:32 - algorithms.model_training - INFO - 开始训练模型: NeuralNet
2025-08-24 09:57:32 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:57:33 - algorithms.model_training - INFO - 模型 NeuralNet 训练完成，用时 0.44秒
2025-08-24 09:57:33 - algorithms.model_training - INFO - R²: 0.9914, MSE: 291.7895
2025-08-24 09:57:33 - algorithms.model_training - INFO - 模型 NeuralNet 的结果已缓存到: D:\Code\MM01U\refactored_ml_platform\cache\NeuralNet_results.joblib
