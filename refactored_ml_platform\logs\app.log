2025-08-07 12:44:38,892 - gui.core.config_manager - INFO - 配置文件不存在，使用默认配置
2025-08-07 12:44:38,894 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:44:39,266 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 12:44:39,270 - __main__ - INFO - 启动重构版机器学习平台
2025-08-07 12:45:12,143 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:45:12,143 - __main__ - INFO - 应用程序正在退出
2025-08-07 12:45:17,246 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:45:17,588 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 12:45:17,588 - __main__ - INFO - 启动重构版机器学习平台
2025-08-07 12:45:36,304 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:45:36,304 - __main__ - INFO - 应用程序正在退出
2025-08-07 12:59:07,306 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 12:59:07,306 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:23:41,982 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:23:41,983 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:23:42,385 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:23:42,385 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:24:41,868 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:24:41,869 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:24:45,975 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:24:45,976 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:24:46,405 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:24:46,405 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:25:47,748 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:25:47,749 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:27:44,673 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:27:44,674 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:27:45,092 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:27:45,092 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:27:55,804 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:27:55,805 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:38:18,229 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:38:18,230 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:38:18,649 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:38:18,649 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:40:11,729 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:40:11,730 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:43:04,006 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:43:04,007 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:43:36,732 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:43:36,733 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:43:37,226 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:43:37,226 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:45:21,410 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:45:21,411 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:49:03,468 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:49:03,469 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:51:39,282 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:51:39,282 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:51:39,828 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:51:39,828 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:53:15,328 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:53:15,328 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:53:15,928 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:53:15,928 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:53:31,273 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:53:32,650 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:53:32,652 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 13:53:32,663 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:53:32,663 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:55:22,083 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 13:55:22,085 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 13:55:22,085 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 13:55:22,247 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.12秒
2025-08-07 13:55:22,248 - core.model_trainer - INFO - 训练完成！成功训练 1 个模型
2025-08-07 13:56:00,114 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 13:56:00,118 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 13:56:00,118 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 13:56:00,122 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 13:56:00,123 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 13:56:00,311 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.12秒
2025-08-07 13:56:00,312 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 13:56:00,374 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.06秒
2025-08-07 13:56:00,375 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 13:56:01,911 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.54秒
2025-08-07 13:56:01,911 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 13:56:02,194 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.28秒
2025-08-07 13:56:02,195 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 13:56:02,290 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.09秒
2025-08-07 13:56:02,291 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 13:56:02,300 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 13:56:02,302 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 13:56:02,304 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 13:56:02,312 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 13:56:02,312 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 13:56:02,321 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 13:56:02,402 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 13:56:02,402 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 13:56:51,647 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:56:51,649 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:56:54,240 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:56:54,247 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:57:05,536 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:57:05,536 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:57:06,124 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:57:06,124 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:57:17,788 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:57:19,235 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:57:19,235 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 13:57:19,249 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:57:19,249 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:57:37,442 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 13:57:37,443 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 13:57:37,444 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 13:57:37,446 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 13:57:37,447 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 13:57:37,610 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.11秒
2025-08-07 13:57:37,611 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 13:57:37,660 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 13:57:37,661 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 13:57:39,041 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.37秒
2025-08-07 13:57:39,043 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 13:57:39,308 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.26秒
2025-08-07 13:57:39,308 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 13:57:39,726 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.41秒
2025-08-07 13:57:39,727 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 13:57:39,737 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 13:57:39,738 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 13:57:39,748 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 13:57:39,750 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 13:57:39,750 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 13:57:39,758 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 13:57:39,839 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 13:57:39,840 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:00:46,103 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:00:46,105 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:05,425 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:04:05,426 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:05,982 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:04:05,982 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:04:34,719 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:04:34,720 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:43,299 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:04:43,301 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:43,879 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:04:43,880 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:05:01,758 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:05:03,184 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:05:03,185 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:05:03,198 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:05:03,198 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:05:17,200 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:05:17,205 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:05:28,545 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:05:28,546 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:05:28,546 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:05:28,550 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:05:28,551 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:05:28,729 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.11秒
2025-08-07 14:05:28,729 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:05:28,787 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 14:05:28,788 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:05:30,158 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.36秒
2025-08-07 14:05:30,160 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:05:30,409 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.24秒
2025-08-07 14:05:30,410 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:05:30,845 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.43秒
2025-08-07 14:05:30,849 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:05:30,852 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:05:30,858 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:05:30,861 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:05:30,870 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:05:30,877 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:05:30,879 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:05:30,961 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 14:05:30,961 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:08:19,150 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:08:19,152 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:11:59,746 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:11:59,746 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:12:00,290 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:12:00,290 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:13:25,784 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:13:26,912 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:13:26,912 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:13:26,924 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:13:26,925 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:13:33,195 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:13:33,200 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:13:38,970 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:13:38,970 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:13:38,970 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:13:38,980 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.01秒
2025-08-07 14:13:38,981 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:13:39,124 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.11秒
2025-08-07 14:13:39,125 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:13:39,250 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.12秒
2025-08-07 14:13:39,251 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:13:40,681 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.43秒
2025-08-07 14:13:40,683 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:13:40,921 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.24秒
2025-08-07 14:13:40,925 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:13:40,926 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.00秒
2025-08-07 14:13:40,932 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:13:40,935 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:13:40,938 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:13:40,942 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:13:40,942 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:13:40,942 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:13:40,943 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:13:41,048 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.10秒
2025-08-07 14:13:41,049 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:13:49,476 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:13:49,479 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:14:45,475 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:14:45,475 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:14:46,057 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:14:46,057 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:14:59,551 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:15:00,635 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:15:00,635 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:15:00,650 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:15:00,651 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:15:08,675 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:15:08,678 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:15:21,951 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:15:21,953 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:15:21,953 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:15:21,957 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:15:21,957 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:15:22,146 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.12秒
2025-08-07 14:15:22,146 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:15:22,198 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 14:15:22,199 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:15:23,583 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.38秒
2025-08-07 14:15:23,585 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:15:23,791 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.21秒
2025-08-07 14:15:23,791 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:15:23,961 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.16秒
2025-08-07 14:15:23,961 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:15:23,966 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:15:23,971 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:15:23,980 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:15:23,982 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:15:23,983 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:15:23,983 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:15:24,061 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 14:15:24,061 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:16:01,330 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:16:01,330 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:18:26,216 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:18:26,217 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:18:26,821 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:18:26,821 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:18:39,740 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:18:40,983 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:18:40,983 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:18:40,996 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:18:40,996 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:18:46,707 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:18:46,711 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:18:51,256 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:18:51,257 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:18:51,257 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:18:51,257 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:18:51,261 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:18:51,419 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.10秒
2025-08-07 14:18:51,419 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:18:51,475 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.06秒
2025-08-07 14:18:51,476 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:18:52,852 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.38秒
2025-08-07 14:18:52,852 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:18:53,040 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.18秒
2025-08-07 14:18:53,040 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:18:53,178 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.14秒
2025-08-07 14:18:53,185 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:18:53,196 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:18:53,197 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:18:53,199 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:18:53,208 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:18:53,208 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:18:53,216 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:18:53,296 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 14:18:53,296 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:19:37,707 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:19:37,707 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:25:34,174 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:25:34,175 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:25:34,770 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:25:34,770 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:26:10,774 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:26:10,781 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:26:16,002 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:26:16,003 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:26:16,558 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:26:16,560 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:26:27,944 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:26:29,181 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:26:29,181 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:26:29,194 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:26:29,195 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:26:38,098 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:26:38,103 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:26:43,390 - ModelTrainerTab - INFO - 检测到任务类型: regression
2025-08-07 14:26:43,391 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:26:43,391 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:26:43,391 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:26:43,395 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:26:43,395 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:26:43,540 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.10秒
2025-08-07 14:26:43,540 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:26:43,593 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 14:26:43,593 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:26:44,958 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.36秒
2025-08-07 14:26:44,960 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:26:45,155 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.19秒
2025-08-07 14:26:45,158 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:26:45,665 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.50秒
2025-08-07 14:26:45,665 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:26:45,667 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:26:45,667 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:26:45,682 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:26:45,685 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:26:45,692 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:26:45,695 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:26:45,768 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.06秒
2025-08-07 14:26:45,783 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:26:56,376 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:26:56,389 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 21:56:05,056 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 21:56:05,057 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 21:59:36,641 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 21:59:36,642 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 21:59:37,244 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 21:59:37,244 - __main__ - INFO - 启动GUI主循环
2025-08-07 22:08:18,422 - __main__ - INFO - 应用程序正在退出
2025-08-07 22:08:18,424 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:13:29,806 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:13:29,806 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:28:52,043 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:28:52,043 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:28:52,375 - refactored_ml_platform.gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:28:52,638 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 23:28:52,638 - __main__ - INFO - 启动GUI主循环
2025-08-07 23:29:29,792 - __main__ - INFO - 应用程序正在退出
2025-08-07 23:29:29,793 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:32:29,723 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:32:29,723 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:32:40,207 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:32:40,208 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:34:17,670 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:34:17,670 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:34:18,098 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 23:34:18,099 - __main__ - INFO - 启动GUI主循环
2025-08-07 23:34:45,572 - __main__ - INFO - 应用程序正在退出
2025-08-07 23:34:45,573 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:11:10,321 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 09:11:10,321 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:11:10,761 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 09:11:10,761 - __main__ - INFO - 启动GUI主循环
2025-08-13 09:12:05,064 - __main__ - INFO - 应用程序正在退出
2025-08-13 09:12:05,064 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:40:03,958 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 09:40:03,960 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:40:04,392 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 09:40:04,392 - __main__ - INFO - 启动GUI主循环
2025-08-13 09:40:27,772 - __main__ - INFO - 应用程序正在退出
2025-08-13 09:40:27,774 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:43:02,622 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 09:43:02,622 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:43:03,095 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 09:43:03,095 - __main__ - INFO - 启动GUI主循环
2025-08-13 09:43:14,228 - __main__ - INFO - 应用程序正在退出
2025-08-13 09:43:14,228 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 10:23:34,526 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 10:23:34,526 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 10:23:34,972 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 10:23:34,972 - __main__ - INFO - 启动GUI主循环
2025-08-13 10:23:54,214 - __main__ - INFO - 应用程序正在退出
2025-08-13 10:23:54,215 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 11:48:34,582 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-23 11:48:34,584 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 11:48:35,062 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 11:48:35,062 - __main__ - INFO - 启动GUI主循环
2025-08-23 11:49:25,359 - __main__ - INFO - 应用程序正在退出
2025-08-23 11:49:25,359 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:02:43,377 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-23 12:02:43,377 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:02:43,862 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:02:43,862 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:03:29,565 - ModelTrainingManager - INFO - 模型训练模块已接收数据预处理完成事件
2025-08-23 12:03:37,960 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:03:37,961 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:10:17,931 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-23 12:10:17,931 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:10:18,411 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:10:18,411 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:10:24,359 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:10:24,361 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:24:36,653 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 12:24:36,658 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:24:37,142 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:24:37,142 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:33:41,360 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:33:41,362 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:41:12,060 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 12:41:12,060 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:41:12,560 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:41:12,560 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:41:32,473 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:41:32,474 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:47:37,409 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 12:47:37,409 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:47:37,895 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:47:37,895 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:47:55,582 - ModelTrainingManager - INFO - 模型训练模块已接收数据预处理完成事件
2025-08-23 12:48:12,089 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:48:12,090 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:57:13,534 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 12:57:13,535 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:57:14,026 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:57:14,026 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:57:31,328 - ModelTrainingManager - INFO - 模型训练模块已接收数据预处理完成事件
2025-08-23 12:57:47,122 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:57:47,123 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 13:06:02,893 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 13:06:02,893 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 13:06:03,374 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 13:06:03,374 - __main__ - INFO - 启动GUI主循环
2025-08-23 13:06:12,251 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-23 13:06:13,219 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-23 13:06:16,215 - ModelTrainingManager - INFO - 模型训练模块已接收预处理数据: 训练集157行, 测试集40行
2025-08-23 13:06:43,578 - __main__ - INFO - 应用程序正在退出
2025-08-23 13:06:43,579 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 14:39:17,811 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 14:39:19,173 - __main__ - ERROR - GUI模块导入失败: attempted relative import beyond top-level package
2025-08-23 14:39:24,928 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 14:39:26,230 - __main__ - ERROR - GUI模块导入失败: attempted relative import beyond top-level package
2025-08-23 15:13:08,046 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 15:13:09,624 - __main__ - ERROR - GUI模块导入失败: No module named 'core.event_manager'
2025-08-23 15:13:33,884 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 15:13:35,291 - __main__ - ERROR - GUI模块导入失败: No module named 'core.event_manager'
2025-08-23 15:17:22,161 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:17:23,577 - __main__ - ERROR - GUI模块导入失败: attempted relative import beyond top-level package
2025-08-23 15:17:25,384 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:17:26,748 - __main__ - ERROR - GUI模块导入失败: attempted relative import beyond top-level package
2025-08-23 15:21:02,624 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:21:04,158 - __main__ - ERROR - GUI模块导入失败: cannot import name 'get_model_manager' from 'core.model_manager' (D:\Code\MM01U\refactored_ml_platform\core\model_manager.py)
2025-08-23 15:21:50,128 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:22:18,056 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:22:19,908 - __main__ - ERROR - GUI模块导入失败: No module named 'utils.data_loader'
2025-08-23 15:23:03,336 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:23:05,167 - __main__ - ERROR - GUI模块导入失败: No module named 'chardet'
2025-08-23 15:23:44,659 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:23:46,488 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:24:54,968 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:24:56,778 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:24:57,317 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:24:57,318 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:25:25,945 - __main__ - INFO - 收到键盘中断信号
2025-08-23 15:25:25,945 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:25:25,949 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:26:06,826 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 15:26:08,532 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:26:09,041 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:26:09,041 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:26:44,094 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:26:44,095 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:35:19,041 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:35:20,876 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:35:21,400 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:35:21,400 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:35:50,007 - __main__ - INFO - 收到键盘中断信号
2025-08-23 15:35:50,008 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:35:50,015 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:42:42,064 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:42:43,925 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:42:44,518 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:42:44,518 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:43:20,405 - __main__ - INFO - 收到键盘中断信号
2025-08-23 15:43:20,405 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:43:20,406 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:45:35,922 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:45:37,793 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:45:38,359 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:45:38,359 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:46:05,904 - __main__ - INFO - 收到键盘中断信号
2025-08-23 15:46:05,904 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:46:05,945 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:48:35,020 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:48:36,868 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:48:37,462 - MainWindow - WARNING - 会话管理模块导入失败: cannot import name 'get_config_manager' from 'core.config_manager' (D:\Code\MM01U\refactored_ml_platform\core\config_manager.py)
2025-08-23 15:49:35,693 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:49:37,591 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:49:38,165 - MainWindow - WARNING - 会话管理模块导入失败: No module named 'core.event_manager'
2025-08-23 15:49:38,186 - MainWindow - WARNING - 报告生成模块导入失败: No module named 'core.event_manager'
2025-08-23 15:49:38,192 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:49:38,192 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:50:06,231 - __main__ - INFO - 收到键盘中断信号
2025-08-23 15:50:06,231 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:50:06,232 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:51:52,446 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 15:51:54,188 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:51:54,719 - MainWindow - WARNING - 会话管理模块导入失败: No module named 'core.event_manager'
2025-08-23 15:51:54,733 - MainWindow - WARNING - 报告生成模块导入失败: No module named 'core.event_manager'
2025-08-23 15:51:54,738 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:51:54,738 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:52:25,173 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:52:25,174 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:54:38,813 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:54:40,696 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:54:43,448 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:54:45,272 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:57:17,550 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 15:57:19,448 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 15:57:20,142 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 15:57:20,142 - __main__ - INFO - 启动GUI主循环
2025-08-23 15:57:47,778 - __main__ - INFO - 收到键盘中断信号
2025-08-23 15:57:47,778 - __main__ - INFO - 应用程序正在退出
2025-08-23 15:57:47,779 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:02:20,073 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:02:21,960 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:02:22,752 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:02:22,752 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:02:59,779 - __main__ - INFO - 收到键盘中断信号
2025-08-23 16:02:59,779 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:02:59,782 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:10:26,948 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:10:28,801 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:10:29,519 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:10:29,519 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:11:47,191 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:11:47,192 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:13:31,903 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:13:33,767 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:13:34,484 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:13:34,484 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:13:49,291 - __main__ - INFO - 收到键盘中断信号
2025-08-23 16:13:49,291 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:13:49,292 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:21:45,777 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:21:46,827 - __main__ - ERROR - GUI模块导入失败: attempted relative import beyond top-level package
2025-08-23 16:22:07,371 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:22:09,194 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:22:40,626 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:22:42,463 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:22:43,179 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:22:43,179 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:22:58,707 - __main__ - INFO - 收到键盘中断信号
2025-08-23 16:22:58,707 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:22:58,708 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:26:25,425 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:26:27,290 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:26:57,321 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:26:59,230 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:26:59,957 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:26:59,957 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:32:11,542 - __main__ - INFO - 收到键盘中断信号
2025-08-23 16:32:11,542 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:32:11,544 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:35:41,232 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:35:43,075 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:35:43,808 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:35:43,808 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:36:58,566 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:36:58,567 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:44:20,197 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 16:44:22,061 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:44:22,608 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 16:44:22,609 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 16:44:22,762 - utils.error_handler - ERROR - 错误上下文: 刷新数据
异常类型: AttributeError
异常消息: 'ModelManager' object has no attribute 'list_models'
2025-08-23 16:44:22,763 - utils.error_handler - INFO - 用户消息: 发生未知错误: 'ModelManager' object has no attribute 'list_models'
2025-08-23 16:44:22,801 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:44:22,801 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:44:34,576 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:44:34,577 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:44:35,100 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 16:44:36,819 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:44:37,398 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 16:44:37,399 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 16:44:37,554 - utils.error_handler - ERROR - 错误上下文: 刷新数据
异常类型: AttributeError
异常消息: 'ModelManager' object has no attribute 'list_models'
2025-08-23 16:44:37,555 - utils.error_handler - INFO - 用户消息: 发生未知错误: 'ModelManager' object has no attribute 'list_models'
2025-08-23 16:44:37,594 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:44:37,594 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:44:45,223 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:44:45,224 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:44:49,235 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 16:44:50,953 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:44:51,487 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 16:44:51,488 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 16:44:51,644 - utils.error_handler - ERROR - 错误上下文: 刷新数据
异常类型: AttributeError
异常消息: 'ModelManager' object has no attribute 'list_models'
2025-08-23 16:44:51,645 - utils.error_handler - INFO - 用户消息: 发生未知错误: 'ModelManager' object has no attribute 'list_models'
2025-08-23 16:44:51,689 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:44:51,690 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:45:09,112 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:45:09,113 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:47:52,275 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:47:54,109 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:47:54,697 - core.config_manager - INFO - 已加载配置文件: D:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 16:47:54,697 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 16:47:54,897 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:47:54,897 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:48:27,920 - __main__ - INFO - 收到键盘中断信号
2025-08-23 16:48:27,920 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:48:27,921 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:49:03,388 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 16:49:05,297 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:49:05,869 - core.config_manager - INFO - 已加载配置文件: D:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 16:49:05,869 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 16:49:06,070 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:49:06,070 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:49:20,148 - __main__ - INFO - 收到键盘中断信号
2025-08-23 16:49:20,148 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:49:20,150 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:50:14,201 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 16:50:15,945 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 16:50:16,483 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 16:50:16,483 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 16:50:16,670 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 16:50:16,670 - __main__ - INFO - 启动GUI主循环
2025-08-23 16:50:38,887 - __main__ - INFO - 应用程序正在退出
2025-08-23 16:50:38,887 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:27:03,083 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:27:04,820 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:27:05,383 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:27:05,384 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:27:05,593 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:27:05,593 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:28:44,348 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:28:46,077 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:28:46,654 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:28:46,654 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:28:46,863 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:28:46,863 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:29:09,135 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:29:09,137 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:34:31,480 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:34:33,193 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:34:33,764 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:34:33,764 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:34:33,902 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:34:33,902 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:34:40,393 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:34:40,394 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:34:58,259 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:34:59,993 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:35:00,537 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:35:00,537 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:35:00,681 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:35:00,681 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:35:03,528 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:35:03,529 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:35:14,500 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:35:16,218 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:35:16,835 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:35:16,836 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:35:17,053 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:35:17,053 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:35:22,800 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:35:22,801 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:36:11,857 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:36:13,580 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:36:14,166 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:36:14,166 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:36:14,390 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:36:14,390 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:36:19,376 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:36:19,377 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:37:01,073 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:37:02,841 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:37:03,421 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:37:03,421 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:37:03,636 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:37:03,636 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:37:09,297 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:37:09,298 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:38:52,531 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:38:54,246 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:38:54,819 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:38:54,819 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:38:55,023 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:38:55,023 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:39:09,001 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:39:09,002 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:40:07,020 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 17:40:08,752 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 17:40:09,354 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 17:40:09,355 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 17:40:09,567 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 17:40:09,567 - __main__ - INFO - 启动GUI主循环
2025-08-23 17:40:27,693 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-23 17:40:27,703 - VisualizationManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-23 17:40:28,586 - algorithms.data_preprocessing - INFO - 成功加载数据: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv, 形状: (197, 9)
2025-08-23 17:40:28,587 - algorithms.data_preprocessing - INFO - 列名清理完成，共处理 9 列
2025-08-23 17:40:28,588 - algorithms.data_preprocessing - INFO - 自动检测到目标列: label
2025-08-23 17:40:28,616 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-23 17:40:28,616 - VisualizationManager - INFO - 可视化模块已加载数据: (197, 9)
2025-08-23 17:40:28,617 - EnsembleManager - INFO - 集成学习模块已加载数据: (197, 9)
2025-08-23 17:40:35,823 - DataManagementModule - INFO - 数据验证完成，质量评分: 85
2025-08-23 17:40:50,890 - DataManagementModule - INFO - 数据已重置到原始状态
2025-08-23 17:40:50,891 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-23 17:40:50,891 - VisualizationManager - INFO - 可视化模块已加载数据: (197, 9)
2025-08-23 17:40:50,892 - EnsembleManager - INFO - 集成学习模块已加载数据: (197, 9)
2025-08-23 17:40:55,201 - DataManagementModule - INFO - 开始数据预处理
2025-08-23 17:40:56,523 - ModelTrainingManager - INFO - 模型训练模块已接收预处理数据: 训练集157行, 测试集40行
2025-08-23 17:40:56,524 - VisualizationManager - INFO - 可视化模块已接收预处理数据: (197, 9)
2025-08-23 17:40:56,524 - EnsembleManager - INFO - 集成学习模块已接收预处理数据
2025-08-23 17:40:56,529 - DataManagementModule - INFO - 数据预处理完成: 5 个步骤
2025-08-23 17:41:11,341 - algorithms.model_training - INFO - 开始训练模型: RandomForest
2025-08-23 17:41:11,342 - algorithms.model_training - WARNING - [RandomForest] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-23 17:41:11,506 - algorithms.model_training - INFO - 模型 RandomForest 训练完成，用时 0.17秒
2025-08-23 17:41:11,506 - algorithms.model_training - INFO - 准确率: 0.8500, AUC: 0.9514066496163682
2025-08-23 17:41:11,528 - algorithms.model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\RandomForest_results.joblib
2025-08-23 17:41:18,764 - algorithms.model_training - INFO - 开始训练模型: RandomForest
2025-08-23 17:41:18,764 - algorithms.model_training - WARNING - [RandomForest] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-23 17:41:18,921 - algorithms.model_training - INFO - 模型 RandomForest 训练完成，用时 0.16秒
2025-08-23 17:41:18,921 - algorithms.model_training - INFO - 准确率: 0.8500, AUC: 0.9514066496163682
2025-08-23 17:41:18,942 - algorithms.model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\RandomForest_results.joblib
2025-08-23 17:42:01,560 - __main__ - INFO - 应用程序正在退出
2025-08-23 17:42:01,561 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:15:47,704 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 18:15:49,458 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:15:50,079 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 18:15:50,080 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 18:15:50,280 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 18:15:50,281 - __main__ - INFO - 启动GUI主循环
2025-08-23 18:16:45,177 - __main__ - INFO - 应用程序正在退出
2025-08-23 18:16:45,178 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:25:45,438 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 18:25:47,197 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:25:47,794 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 18:25:47,794 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 18:25:48,028 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 18:25:48,028 - __main__ - INFO - 启动GUI主循环
2025-08-23 18:26:44,081 - __main__ - INFO - 应用程序正在退出
2025-08-23 18:26:44,083 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:31:18,555 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 18:31:20,461 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:31:21,084 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 18:31:21,085 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 18:31:21,324 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 18:31:21,325 - __main__ - INFO - 启动GUI主循环
2025-08-23 18:31:45,496 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-23 18:31:45,500 - VisualizationManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-23 18:31:46,187 - algorithms.data_preprocessing - INFO - 成功加载数据: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv, 形状: (197, 9)
2025-08-23 18:31:46,192 - algorithms.data_preprocessing - INFO - 列名清理完成，共处理 9 列
2025-08-23 18:31:46,192 - algorithms.data_preprocessing - INFO - 自动检测到目标列: label
2025-08-23 18:31:46,223 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-23 18:31:46,223 - VisualizationManager - INFO - 可视化模块已加载数据: (197, 9)
2025-08-23 18:31:46,225 - EnsembleManager - INFO - 集成学习模块已加载数据: (197, 9)
2025-08-23 18:31:46,228 - HyperparameterTuningManager - INFO - 超参数调优模块已加载数据: (197, 9)
2025-08-23 18:31:53,425 - DataManagementModule - INFO - 数据验证完成，质量评分: 85
2025-08-23 18:32:03,138 - DataManagementModule - INFO - 开始数据预处理
2025-08-23 18:32:04,276 - ModelTrainingManager - INFO - 模型训练模块已接收预处理数据: 训练集157行, 测试集40行
2025-08-23 18:32:04,276 - VisualizationManager - INFO - 可视化模块已接收预处理数据: (197, 9)
2025-08-23 18:32:04,278 - EnsembleManager - INFO - 集成学习模块已接收预处理数据
2025-08-23 18:32:04,280 - HyperparameterTuningManager - INFO - 超参数调优模块已接收预处理数据
2025-08-23 18:32:04,286 - DataManagementModule - INFO - 数据预处理完成: 5 个步骤
2025-08-23 18:32:15,175 - algorithms.model_training - INFO - 开始训练模型: RandomForest
2025-08-23 18:32:15,175 - algorithms.model_training - WARNING - [RandomForest] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-23 18:32:15,356 - algorithms.model_training - INFO - 模型 RandomForest 训练完成，用时 0.18秒
2025-08-23 18:32:15,357 - algorithms.model_training - INFO - 准确率: 0.8500, AUC: 0.9514066496163682
2025-08-23 18:32:15,379 - algorithms.model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\RandomForest_results.joblib
2025-08-23 18:32:15,383 - algorithms.model_training - INFO - 开始训练模型: XGBoost
2025-08-23 18:32:15,384 - algorithms.model_training - WARNING - [XGBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-23 18:32:15,431 - algorithms.model_training - INFO - 模型 XGBoost 训练完成，用时 0.05秒
2025-08-23 18:32:15,432 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.9667519181585678
2025-08-23 18:32:15,440 - algorithms.model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\XGBoost_results.joblib
2025-08-23 18:32:15,442 - ModelTrainingManager - WARNING - 模型 LogisticRegression 不可用
2025-08-23 18:32:40,745 - __main__ - INFO - 应用程序正在退出
2025-08-23 18:32:40,747 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:35:35,563 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 18:35:37,310 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 18:35:37,901 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 18:35:37,901 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 18:35:38,135 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 18:35:38,135 - __main__ - INFO - 启动GUI主循环
2025-08-23 18:36:26,474 - __main__ - INFO - 应用程序正在退出
2025-08-23 18:36:26,475 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 19:21:41,794 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 19:21:43,677 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 19:21:44,128 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-23 19:21:44,284 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-23 19:21:44,305 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-23 19:21:44,400 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 19:21:44,400 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 19:21:44,557 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 19:21:44,557 - __main__ - INFO - 启动GUI主循环
2025-08-23 19:22:26,666 - __main__ - INFO - 应用程序正在退出
2025-08-23 19:22:26,667 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 19:23:16,348 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 19:23:18,196 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 19:23:18,652 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-23 19:23:18,763 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-23 19:23:18,822 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-23 19:23:18,917 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-23 19:23:18,917 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-23 19:23:19,074 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 19:23:19,074 - __main__ - INFO - 启动GUI主循环
2025-08-23 19:23:46,178 - __main__ - INFO - 应用程序正在退出
2025-08-23 19:23:46,179 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:19:04,961 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-24 09:19:07,974 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:19:08,421 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:19:08,528 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:19:08,585 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:19:08,678 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:19:08,678 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:19:08,827 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:19:08,827 - __main__ - INFO - 启动GUI主循环
2025-08-24 09:19:51,838 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:19:51,844 - VisualizationManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:19:56,856 - algorithms.data_preprocessing - INFO - 成功加载数据: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv, 形状: (197, 9)
2025-08-24 09:19:56,857 - algorithms.data_preprocessing - INFO - 列名清理完成，共处理 9 列
2025-08-24 09:19:56,862 - algorithms.data_preprocessing - INFO - 自动检测到目标列: label
2025-08-24 09:19:56,900 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-24 09:19:56,900 - VisualizationManager - INFO - 可视化模块已加载数据: (197, 9)
2025-08-24 09:19:56,902 - EnsembleManager - INFO - 集成学习模块已加载数据: (197, 9)
2025-08-24 09:19:56,904 - HyperparameterTuningManager - INFO - 超参数调优模块已加载数据: (197, 9)
2025-08-24 09:20:02,210 - gui.components.progress_widget - INFO - 进度开始: 正在加载数据...
2025-08-24 09:20:02,211 - gui.modules.data_exploration - ERROR - 数据加载失败: 'EventManager' object has no attribute 'get_data'
2025-08-24 09:20:02,211 - gui.components.progress_widget - ERROR - 进度错误: 数据加载失败: 'EventManager' object has no attribute 'get_data'
2025-08-24 09:20:40,329 - gui.components.progress_widget - INFO - 进度开始: 正在加载数据...
2025-08-24 09:20:40,333 - utils.data_loader - INFO - 成功加载CSV文件: C:\Users\<USER>\Desktop\parameters\0807\nodule2.csv, 形状: (197, 9)
2025-08-24 09:20:40,342 - utils.data_explorer - INFO - 数据基本信息分析完成，形状: (197, 9)
2025-08-24 09:20:40,342 - gui.modules.data_exploration - INFO - 数据分析完成: 197行, 9列
2025-08-24 09:20:40,350 - gui.components.progress_widget - INFO - 进度完成: 数据加载完成
2025-08-24 09:21:16,478 - algorithms.model_training - INFO - 开始训练模型: DecisionTree
2025-08-24 09:21:16,478 - algorithms.model_training - WARNING - [DecisionTree] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:16,479 - algorithms.model_training - ERROR - 训练模型 DecisionTree 时出错: __init__() got an unexpected keyword argument 'n_jobs'
2025-08-24 09:21:16,479 - ModelTrainingManager - ERROR - 训练模型 DecisionTree 时出错: __init__() got an unexpected keyword argument 'n_jobs'
2025-08-24 09:21:16,481 - algorithms.model_training - INFO - 开始训练模型: RandomForest
2025-08-24 09:21:16,481 - algorithms.model_training - WARNING - [RandomForest] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:16,481 - algorithms.model_training - ERROR - 训练模型 RandomForest 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:16,481 - ModelTrainingManager - ERROR - 训练模型 RandomForest 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:16,490 - algorithms.model_training - INFO - 开始训练模型: XGBoost
2025-08-24 09:21:16,490 - algorithms.model_training - WARNING - [XGBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:16,492 - algorithms.model_training - ERROR - 训练模型 XGBoost 时出错: Invalid classes inferred from unique values of `y`.  Expected: [  0   1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17
  18  19  20  21  22  23  24  25  26  27  28  29  30  31  32  33  34  35
  36  37  38  39  40  41  42  43  44  45  46  47  48  49  50  51  52  53
  54  55  56  57  58  59  60  61  62  63  64  65  66  67  68  69  70  71
  72  73  74  75  76  77  78  79  80  81  82  83  84  85  86  87  88  89
  90  91  92  93  94  95  96  97  98  99 100 101 102 103 104 105 106 107
 108 109 110 111 112 113 114 115 116 117 118 119 120 121 122 123 124 125
 126 127 128 129 130 131 132 133 134 135 136 137 138 139 140 141 142 143], got [ 29.    37.    44.5   45.75  58.5   64.5   67.75  68.    70.25  71.5
  79.    81.25  81.5   87.    88.    89.   100.   105.   107.5  113.
 114.   115.   119.   121.   121.25 122.   129.25 133.   134.   134.5
 136.5  137.5  138.   140.   147.5  151.5  158.   163.   164.5  167.
 169.   169.5  171.   175.   178.5  180.   181.   183.25 187.5  192.
 194.   194.5  196.5  197.   199.   200.   201.   203.   204.   205.
 207.   208.   210.75 211.   212.   214.   215.   216.   217.5  225.
 225.75 227.5  228.   228.5  229.   234.25 235.   238.   239.5  240.
 241.   246.5  247.75 253.5  257.5  260.75 261.   263.   269.   271.25
 272.   276.   278.   279.   288.   292.   297.5  303.5  304.   306.
 309.5  313.5  314.   316.25 321.5  322.   329.   331.25 332.5  333.
 338.   342.   342.5  343.   349.5  353.   357.75 359.5  360.   360.5
 374.   375.   384.5  390.   393.25 395.25 395.5  396.   398.5  405.
 418.   430.   431.75 441.75 450.   457.   459.   460.   491.25 493.75
 495.   524.   592.5  882.  ]
2025-08-24 09:21:16,493 - ModelTrainingManager - ERROR - 训练模型 XGBoost 时出错: Invalid classes inferred from unique values of `y`.  Expected: [  0   1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16  17
  18  19  20  21  22  23  24  25  26  27  28  29  30  31  32  33  34  35
  36  37  38  39  40  41  42  43  44  45  46  47  48  49  50  51  52  53
  54  55  56  57  58  59  60  61  62  63  64  65  66  67  68  69  70  71
  72  73  74  75  76  77  78  79  80  81  82  83  84  85  86  87  88  89
  90  91  92  93  94  95  96  97  98  99 100 101 102 103 104 105 106 107
 108 109 110 111 112 113 114 115 116 117 118 119 120 121 122 123 124 125
 126 127 128 129 130 131 132 133 134 135 136 137 138 139 140 141 142 143], got [ 29.    37.    44.5   45.75  58.5   64.5   67.75  68.    70.25  71.5
  79.    81.25  81.5   87.    88.    89.   100.   105.   107.5  113.
 114.   115.   119.   121.   121.25 122.   129.25 133.   134.   134.5
 136.5  137.5  138.   140.   147.5  151.5  158.   163.   164.5  167.
 169.   169.5  171.   175.   178.5  180.   181.   183.25 187.5  192.
 194.   194.5  196.5  197.   199.   200.   201.   203.   204.   205.
 207.   208.   210.75 211.   212.   214.   215.   216.   217.5  225.
 225.75 227.5  228.   228.5  229.   234.25 235.   238.   239.5  240.
 241.   246.5  247.75 253.5  257.5  260.75 261.   263.   269.   271.25
 272.   276.   278.   279.   288.   292.   297.5  303.5  304.   306.
 309.5  313.5  314.   316.25 321.5  322.   329.   331.25 332.5  333.
 338.   342.   342.5  343.   349.5  353.   357.75 359.5  360.   360.5
 374.   375.   384.5  390.   393.25 395.25 395.5  396.   398.5  405.
 418.   430.   431.75 441.75 450.   457.   459.   460.   491.25 493.75
 495.   524.   592.5  882.  ]
2025-08-24 09:21:16,495 - algorithms.model_training - INFO - 开始训练模型: LightGBM
2025-08-24 09:21:16,495 - algorithms.model_training - WARNING - [LightGBM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:16,496 - algorithms.model_training - ERROR - 训练模型 LightGBM 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:16,496 - ModelTrainingManager - ERROR - 训练模型 LightGBM 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:16,502 - algorithms.model_training - INFO - 开始训练模型: CatBoost
2025-08-24 09:21:16,502 - algorithms.model_training - WARNING - [CatBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:21,462 - algorithms.model_training - ERROR - 训练模型 CatBoost 时出错: continuous is not supported
2025-08-24 09:21:21,462 - ModelTrainingManager - ERROR - 训练模型 CatBoost 时出错: continuous is not supported
2025-08-24 09:21:21,466 - ModelTrainingManager - WARNING - 模型 LogisticRegression 不可用
2025-08-24 09:21:21,468 - algorithms.model_training - INFO - 开始训练模型: SVM
2025-08-24 09:21:21,468 - algorithms.model_training - WARNING - [SVM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:21,469 - algorithms.model_training - ERROR - 训练模型 SVM 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:21,469 - ModelTrainingManager - ERROR - 训练模型 SVM 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:21,478 - algorithms.model_training - INFO - 开始训练模型: KNN
2025-08-24 09:21:21,478 - algorithms.model_training - WARNING - [KNN] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:21,479 - algorithms.model_training - ERROR - 训练模型 KNN 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:21,480 - ModelTrainingManager - ERROR - 训练模型 KNN 时出错: Unknown label type: continuous. Maybe you are trying to fit a classifier, which expects discrete classes on a regression target with continuous values.
2025-08-24 09:21:21,487 - algorithms.model_training - INFO - 开始训练模型: NaiveBayes
2025-08-24 09:21:21,487 - algorithms.model_training - WARNING - [NaiveBayes] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:21,488 - algorithms.model_training - ERROR - 训练模型 NaiveBayes 时出错: Unknown label type: (array([ 29.  ,  37.  ,  44.5 ,  45.75,  58.5 ,  64.5 ,  67.75,  68.  ,
        70.25,  71.5 ,  79.  ,  81.25,  81.5 ,  87.  ,  88.  ,  89.  ,
       100.  , 105.  , 107.5 , 113.  , 114.  , 115.  , 119.  , 121.  ,
       121.25, 122.  , 129.25, 133.  , 134.  , 134.5 , 136.5 , 137.5 ,
       138.  , 140.  , 147.5 , 151.5 , 158.  , 163.  , 164.5 , 167.  ,
       169.  , 169.5 , 171.  , 175.  , 178.5 , 180.  , 181.  , 183.25,
       187.5 , 192.  , 194.  , 194.5 , 196.5 , 197.  , 199.  , 200.  ,
       201.  , 203.  , 204.  , 205.  , 207.  , 208.  , 210.75, 211.  ,
       212.  , 214.  , 215.  , 216.  , 217.5 , 225.  , 225.75, 227.5 ,
       228.  , 228.5 , 229.  , 234.25, 235.  , 238.  , 239.5 , 240.  ,
       241.  , 246.5 , 247.75, 253.5 , 257.5 , 260.75, 261.  , 263.  ,
       269.  , 271.25, 272.  , 276.  , 278.  , 279.  , 288.  , 292.  ,
       297.5 , 303.5 , 304.  , 306.  , 309.5 , 313.5 , 314.  , 316.25,
       321.5 , 322.  , 329.  , 331.25, 332.5 , 333.  , 338.  , 342.  ,
       342.5 , 343.  , 349.5 , 353.  , 357.75, 359.5 , 360.  , 360.5 ,
       374.  , 375.  , 384.5 , 390.  , 393.25, 395.25, 395.5 , 396.  ,
       398.5 , 405.  , 418.  , 430.  , 431.75, 441.75, 450.  , 457.  ,
       459.  , 460.  , 491.25, 493.75, 495.  , 524.  , 592.5 , 882.  ]),)
2025-08-24 09:21:21,488 - ModelTrainingManager - ERROR - 训练模型 NaiveBayes 时出错: Unknown label type: (array([ 29.  ,  37.  ,  44.5 ,  45.75,  58.5 ,  64.5 ,  67.75,  68.  ,
        70.25,  71.5 ,  79.  ,  81.25,  81.5 ,  87.  ,  88.  ,  89.  ,
       100.  , 105.  , 107.5 , 113.  , 114.  , 115.  , 119.  , 121.  ,
       121.25, 122.  , 129.25, 133.  , 134.  , 134.5 , 136.5 , 137.5 ,
       138.  , 140.  , 147.5 , 151.5 , 158.  , 163.  , 164.5 , 167.  ,
       169.  , 169.5 , 171.  , 175.  , 178.5 , 180.  , 181.  , 183.25,
       187.5 , 192.  , 194.  , 194.5 , 196.5 , 197.  , 199.  , 200.  ,
       201.  , 203.  , 204.  , 205.  , 207.  , 208.  , 210.75, 211.  ,
       212.  , 214.  , 215.  , 216.  , 217.5 , 225.  , 225.75, 227.5 ,
       228.  , 228.5 , 229.  , 234.25, 235.  , 238.  , 239.5 , 240.  ,
       241.  , 246.5 , 247.75, 253.5 , 257.5 , 260.75, 261.  , 263.  ,
       269.  , 271.25, 272.  , 276.  , 278.  , 279.  , 288.  , 292.  ,
       297.5 , 303.5 , 304.  , 306.  , 309.5 , 313.5 , 314.  , 316.25,
       321.5 , 322.  , 329.  , 331.25, 332.5 , 333.  , 338.  , 342.  ,
       342.5 , 343.  , 349.5 , 353.  , 357.75, 359.5 , 360.  , 360.5 ,
       374.  , 375.  , 384.5 , 390.  , 393.25, 395.25, 395.5 , 396.  ,
       398.5 , 405.  , 418.  , 430.  , 431.75, 441.75, 450.  , 457.  ,
       459.  , 460.  , 491.25, 493.75, 495.  , 524.  , 592.5 , 882.  ]),)
2025-08-24 09:21:21,496 - algorithms.model_training - INFO - 开始训练模型: NeuralNet
2025-08-24 09:21:21,496 - algorithms.model_training - WARNING - [NeuralNet] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:21:21,498 - algorithms.model_training - ERROR - 训练模型 NeuralNet 时出错: Unknown label type: (array([217.5 , 431.75, 200.  , 215.  , 238.  , 375.  , 322.  , 882.  ,
        81.5 , 390.  , 321.5 ,  44.5 , 158.  , 183.25, 269.  , 272.  ,
       199.  , 271.25, 316.25, 227.5 , 207.  , 524.  , 333.  ,  70.25,
       178.5 , 309.5 , 374.  , 450.  , 225.  , 384.5 , 134.  , 342.5 ,
       136.5 ,  64.5 , 395.5 , 194.5 , 129.25,  87.  , 246.5 , 151.5 ,
       276.  , 261.  , 235.  , 303.5 , 292.  , 192.  , 107.5 , 297.5 ,
       196.5 , 169.5 ,  71.5 , 405.  ,  79.  , 100.  , 304.  , 495.  ,
       260.75, 175.  , 338.  , 306.  , 234.25, 240.  , 140.  , 197.  ,
       279.  , 167.  , 393.25, 329.  , 398.5 , 343.  , 192.  , 171.  ,
       360.  , 121.  , 396.  , 214.  , 353.  , 204.  ,  68.  , 418.  ,
       278.  , 342.  , 113.  , 247.75, 263.  ,  67.75, 211.  , 491.25,
       430.  , 147.5 , 119.  , 121.25, 253.5 ,  29.  , 288.  , 114.  ,
       105.  , 297.5 , 205.  , 215.  ,  88.  , 235.  , 263.  , 100.  ,
       257.5 ,  37.  , 171.  , 138.  , 212.  , 441.75, 225.75, 216.  ,
       331.25, 180.  , 228.5 , 187.5 , 239.5 , 313.5 , 137.5 , 229.  ,
       201.  , 360.5 , 115.  , 332.5 , 357.75,  37.  , 163.  , 460.  ,
        58.5 , 241.  ,  45.75, 457.  , 228.  , 314.  , 169.  , 208.  ,
       359.5 , 353.  , 207.  , 164.5 , 122.  , 181.  , 210.75,  89.  ,
       493.75,  81.25, 194.  , 349.5 , 390.  , 133.  , 194.5 , 134.5 ,
       459.  , 395.25, 203.  , 201.  , 592.5 ]),)
2025-08-24 09:21:21,498 - ModelTrainingManager - ERROR - 训练模型 NeuralNet 时出错: Unknown label type: (array([217.5 , 431.75, 200.  , 215.  , 238.  , 375.  , 322.  , 882.  ,
        81.5 , 390.  , 321.5 ,  44.5 , 158.  , 183.25, 269.  , 272.  ,
       199.  , 271.25, 316.25, 227.5 , 207.  , 524.  , 333.  ,  70.25,
       178.5 , 309.5 , 374.  , 450.  , 225.  , 384.5 , 134.  , 342.5 ,
       136.5 ,  64.5 , 395.5 , 194.5 , 129.25,  87.  , 246.5 , 151.5 ,
       276.  , 261.  , 235.  , 303.5 , 292.  , 192.  , 107.5 , 297.5 ,
       196.5 , 169.5 ,  71.5 , 405.  ,  79.  , 100.  , 304.  , 495.  ,
       260.75, 175.  , 338.  , 306.  , 234.25, 240.  , 140.  , 197.  ,
       279.  , 167.  , 393.25, 329.  , 398.5 , 343.  , 192.  , 171.  ,
       360.  , 121.  , 396.  , 214.  , 353.  , 204.  ,  68.  , 418.  ,
       278.  , 342.  , 113.  , 247.75, 263.  ,  67.75, 211.  , 491.25,
       430.  , 147.5 , 119.  , 121.25, 253.5 ,  29.  , 288.  , 114.  ,
       105.  , 297.5 , 205.  , 215.  ,  88.  , 235.  , 263.  , 100.  ,
       257.5 ,  37.  , 171.  , 138.  , 212.  , 441.75, 225.75, 216.  ,
       331.25, 180.  , 228.5 , 187.5 , 239.5 , 313.5 , 137.5 , 229.  ,
       201.  , 360.5 , 115.  , 332.5 , 357.75,  37.  , 163.  , 460.  ,
        58.5 , 241.  ,  45.75, 457.  , 228.  , 314.  , 169.  , 208.  ,
       359.5 , 353.  , 207.  , 164.5 , 122.  , 181.  , 210.75,  89.  ,
       493.75,  81.25, 194.  , 349.5 , 390.  , 133.  , 194.5 , 134.5 ,
       459.  , 395.25, 203.  , 201.  , 592.5 ]),)
2025-08-24 09:21:44,472 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:21:44,473 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:31:44,596 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-24 09:31:46,402 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:31:46,893 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:31:47,066 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:31:47,090 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:31:47,206 - core.config_manager - INFO - 已加载配置文件: D:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:31:47,206 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:31:47,369 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:31:47,369 - __main__ - INFO - 启动GUI主循环
2025-08-24 09:33:02,376 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:33:02,377 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:33:09,972 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-24 09:33:11,613 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:33:12,099 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:33:12,210 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:33:12,275 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:33:12,363 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:33:12,364 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:33:12,507 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:33:12,508 - __main__ - INFO - 启动GUI主循环
2025-08-24 09:33:21,639 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:33:21,645 - VisualizationManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:33:22,807 - algorithms.data_preprocessing - INFO - 成功加载数据: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv, 形状: (197, 9)
2025-08-24 09:33:22,808 - algorithms.data_preprocessing - INFO - 列名清理完成，共处理 9 列
2025-08-24 09:33:22,808 - algorithms.data_preprocessing - INFO - 自动检测到目标列: label
2025-08-24 09:33:22,836 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-24 09:33:22,837 - VisualizationManager - INFO - 可视化模块已加载数据: (197, 9)
2025-08-24 09:33:22,838 - EnsembleManager - INFO - 集成学习模块已加载数据: (197, 9)
2025-08-24 09:33:22,840 - HyperparameterTuningManager - INFO - 超参数调优模块已加载数据: (197, 9)
2025-08-24 09:33:28,718 - DataManagementModule - INFO - 数据验证完成，质量评分: 85
2025-08-24 09:33:32,840 - DataManagementModule - INFO - 开始数据预处理
2025-08-24 09:33:33,779 - ModelTrainingManager - INFO - 模型训练模块已接收预处理数据: 训练集157行, 测试集40行
2025-08-24 09:33:33,779 - VisualizationManager - INFO - 可视化模块已接收预处理数据: (197, 9)
2025-08-24 09:33:33,783 - EnsembleManager - INFO - 集成学习模块已接收预处理数据
2025-08-24 09:33:33,785 - HyperparameterTuningManager - INFO - 超参数调优模块已接收预处理数据
2025-08-24 09:33:33,791 - DataManagementModule - INFO - 数据预处理完成: 5 个步骤
2025-08-24 09:33:36,661 - gui.components.progress_widget - INFO - 进度开始: 正在加载数据...
2025-08-24 09:33:36,661 - gui.modules.data_exploration - ERROR - 数据加载失败: 没有找到当前数据，请先在数据管理模块加载数据
2025-08-24 09:33:36,666 - gui.components.progress_widget - ERROR - 进度错误: 数据加载失败: 没有找到当前数据，请先在数据管理模块加载数据
2025-08-24 09:34:13,710 - algorithms.model_training - INFO - 开始训练模型: DecisionTree
2025-08-24 09:34:13,710 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:13,710 - algorithms.model_training - WARNING - [DecisionTree] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:13,726 - algorithms.model_training - INFO - 模型 DecisionTree 训练完成，用时 0.02秒
2025-08-24 09:34:13,726 - algorithms.model_training - INFO - 准确率: 0.8000, AUC: 0.9002557544757034
2025-08-24 09:34:13,729 - algorithms.model_training - INFO - 模型 DecisionTree 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\DecisionTree_results.joblib
2025-08-24 09:34:13,741 - algorithms.model_training - INFO - 开始训练模型: RandomForest
2025-08-24 09:34:13,741 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:13,741 - algorithms.model_training - WARNING - [RandomForest] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:13,888 - algorithms.model_training - INFO - 模型 RandomForest 训练完成，用时 0.15秒
2025-08-24 09:34:13,888 - algorithms.model_training - INFO - 准确率: 0.8500, AUC: 0.948849104859335
2025-08-24 09:34:13,909 - algorithms.model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\RandomForest_results.joblib
2025-08-24 09:34:13,914 - algorithms.model_training - INFO - 开始训练模型: XGBoost
2025-08-24 09:34:13,915 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:13,915 - algorithms.model_training - WARNING - [XGBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:13,960 - algorithms.model_training - INFO - 模型 XGBoost 训练完成，用时 0.05秒
2025-08-24 09:34:13,960 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.9667519181585678
2025-08-24 09:34:13,965 - algorithms.model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\XGBoost_results.joblib
2025-08-24 09:34:13,973 - algorithms.model_training - INFO - 开始训练模型: LightGBM
2025-08-24 09:34:13,974 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:13,974 - algorithms.model_training - WARNING - [LightGBM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:14,033 - algorithms.model_training - INFO - 模型 LightGBM 训练完成，用时 0.06秒
2025-08-24 09:34:14,033 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.9462915601023019
2025-08-24 09:34:14,039 - algorithms.model_training - INFO - 模型 LightGBM 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\LightGBM_results.joblib
2025-08-24 09:34:14,042 - algorithms.model_training - INFO - 开始训练模型: CatBoost
2025-08-24 09:34:14,043 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:14,043 - algorithms.model_training - WARNING - [CatBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:14,238 - algorithms.model_training - INFO - 模型 CatBoost 训练完成，用时 0.20秒
2025-08-24 09:34:14,238 - algorithms.model_training - INFO - 准确率: 0.9250, AUC: 0.9820971867007673
2025-08-24 09:34:14,240 - algorithms.model_training - INFO - 模型 CatBoost 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\CatBoost_results.joblib
2025-08-24 09:34:14,243 - ModelTrainingManager - WARNING - 模型 LogisticRegression 不可用
2025-08-24 09:34:14,251 - algorithms.model_training - INFO - 开始训练模型: SVM
2025-08-24 09:34:14,251 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:14,251 - algorithms.model_training - WARNING - [SVM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:14,263 - algorithms.model_training - INFO - 模型 SVM 训练完成，用时 0.01秒
2025-08-24 09:34:14,263 - algorithms.model_training - INFO - 准确率: 0.8000, AUC: 0.9207161125319694
2025-08-24 09:34:14,266 - algorithms.model_training - INFO - 模型 SVM 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\SVM_results.joblib
2025-08-24 09:34:14,268 - algorithms.model_training - INFO - 开始训练模型: KNN
2025-08-24 09:34:14,268 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:14,268 - algorithms.model_training - WARNING - [KNN] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:14,278 - algorithms.model_training - INFO - 模型 KNN 训练完成，用时 0.01秒
2025-08-24 09:34:14,278 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.9322250639386189
2025-08-24 09:34:14,280 - algorithms.model_training - INFO - 模型 KNN 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\KNN_results.joblib
2025-08-24 09:34:14,282 - algorithms.model_training - INFO - 开始训练模型: NaiveBayes
2025-08-24 09:34:14,283 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:14,283 - algorithms.model_training - WARNING - [NaiveBayes] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:14,292 - algorithms.model_training - INFO - 模型 NaiveBayes 训练完成，用时 0.01秒
2025-08-24 09:34:14,292 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.89769820971867
2025-08-24 09:34:14,294 - algorithms.model_training - INFO - 模型 NaiveBayes 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\NaiveBayes_results.joblib
2025-08-24 09:34:14,301 - algorithms.model_training - INFO - 开始训练模型: NeuralNet
2025-08-24 09:34:14,301 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:34:14,301 - algorithms.model_training - WARNING - [NeuralNet] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:34:14,550 - algorithms.model_training - INFO - 模型 NeuralNet 训练完成，用时 0.25秒
2025-08-24 09:34:14,550 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.959079283887468
2025-08-24 09:34:14,557 - algorithms.model_training - INFO - 模型 NeuralNet 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\NeuralNet_results.joblib
2025-08-24 09:34:57,783 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:34:57,784 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:40:42,391 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-24 09:40:44,112 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:40:44,568 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:40:44,685 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:40:44,745 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:40:44,836 - core.config_manager - INFO - 已加载配置文件: D:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:40:44,836 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:40:44,986 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:40:44,987 - __main__ - INFO - 启动GUI主循环
2025-08-24 09:41:53,346 - __main__ - INFO - 收到键盘中断信号
2025-08-24 09:41:53,346 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:41:53,346 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:43:38,059 - __main__ - INFO - 收到键盘中断信号
2025-08-24 09:43:38,059 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:43:38,060 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:43:38,060 - __main__ - WARNING - 销毁主窗口时出错: can't invoke "destroy" command: application has been destroyed
2025-08-24 09:43:38,060 - __main__ - ERROR - 退出时发生错误: can't invoke "destroy" command: application has been destroyed
2025-08-24 09:43:55,817 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-24 09:43:57,473 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:43:57,933 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:43:58,041 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:43:58,100 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:43:58,189 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:43:58,190 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:43:58,333 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:43:58,333 - __main__ - INFO - 启动GUI主循环
2025-08-24 09:44:07,027 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:44:07,037 - VisualizationManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:44:08,190 - algorithms.data_preprocessing - INFO - 成功加载数据: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv, 形状: (197, 9)
2025-08-24 09:44:08,191 - algorithms.data_preprocessing - INFO - 列名清理完成，共处理 9 列
2025-08-24 09:44:08,191 - algorithms.data_preprocessing - INFO - 自动检测到目标列: label
2025-08-24 09:44:08,220 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-24 09:44:08,220 - VisualizationManager - INFO - 可视化模块已加载数据: (197, 9)
2025-08-24 09:44:08,222 - EnsembleManager - INFO - 集成学习模块已加载数据: (197, 9)
2025-08-24 09:44:08,223 - HyperparameterTuningManager - INFO - 超参数调优模块已加载数据: (197, 9)
2025-08-24 09:44:13,993 - DataManagementModule - INFO - 数据验证完成，质量评分: 85
2025-08-24 09:44:16,392 - DataManagementModule - INFO - 开始数据预处理
2025-08-24 09:44:17,531 - ModelTrainingManager - INFO - 模型训练模块已接收预处理数据: 训练集157行, 测试集40行
2025-08-24 09:44:17,531 - VisualizationManager - INFO - 可视化模块已接收预处理数据: (197, 9)
2025-08-24 09:44:17,534 - EnsembleManager - INFO - 集成学习模块已接收预处理数据
2025-08-24 09:44:17,537 - HyperparameterTuningManager - INFO - 超参数调优模块已接收预处理数据
2025-08-24 09:44:17,541 - DataManagementModule - INFO - 数据预处理完成: 5 个步骤
2025-08-24 09:44:20,249 - gui.components.progress_widget - INFO - 进度开始: 正在加载数据...
2025-08-24 09:44:20,251 - utils.data_explorer - INFO - 数据基本信息分析完成，形状: (197, 9)
2025-08-24 09:44:20,251 - gui.modules.data_exploration - INFO - 数据分析完成: 197行, 9列
2025-08-24 09:44:20,256 - gui.components.progress_widget - INFO - 进度完成: 数据加载完成
2025-08-24 09:45:07,565 - algorithms.model_training - INFO - 开始训练模型: DecisionTree
2025-08-24 09:45:07,565 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:07,565 - algorithms.model_training - WARNING - [DecisionTree] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:07,575 - algorithms.model_training - INFO - 模型 DecisionTree 训练完成，用时 0.01秒
2025-08-24 09:45:07,576 - algorithms.model_training - INFO - 准确率: 0.8000, AUC: 0.9002557544757034
2025-08-24 09:45:07,578 - algorithms.model_training - INFO - 模型 DecisionTree 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\DecisionTree_results.joblib
2025-08-24 09:45:07,583 - algorithms.model_training - INFO - 开始训练模型: RandomForest
2025-08-24 09:45:07,583 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:07,583 - algorithms.model_training - WARNING - [RandomForest] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:07,755 - algorithms.model_training - INFO - 模型 RandomForest 训练完成，用时 0.17秒
2025-08-24 09:45:07,755 - algorithms.model_training - INFO - 准确率: 0.8500, AUC: 0.948849104859335
2025-08-24 09:45:07,777 - algorithms.model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\RandomForest_results.joblib
2025-08-24 09:45:07,779 - algorithms.model_training - INFO - 开始训练模型: XGBoost
2025-08-24 09:45:07,780 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:07,780 - algorithms.model_training - WARNING - [XGBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:07,833 - algorithms.model_training - INFO - 模型 XGBoost 训练完成，用时 0.05秒
2025-08-24 09:45:07,833 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.9667519181585678
2025-08-24 09:45:07,839 - algorithms.model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\XGBoost_results.joblib
2025-08-24 09:45:07,842 - algorithms.model_training - INFO - 开始训练模型: LightGBM
2025-08-24 09:45:07,842 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:07,842 - algorithms.model_training - WARNING - [LightGBM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:07,914 - algorithms.model_training - INFO - 模型 LightGBM 训练完成，用时 0.07秒
2025-08-24 09:45:07,914 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.9462915601023019
2025-08-24 09:45:07,920 - algorithms.model_training - INFO - 模型 LightGBM 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\LightGBM_results.joblib
2025-08-24 09:45:07,923 - algorithms.model_training - INFO - 开始训练模型: CatBoost
2025-08-24 09:45:07,923 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:07,923 - algorithms.model_training - WARNING - [CatBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:08,266 - algorithms.model_training - INFO - 模型 CatBoost 训练完成，用时 0.34秒
2025-08-24 09:45:08,266 - algorithms.model_training - INFO - 准确率: 0.9250, AUC: 0.9820971867007673
2025-08-24 09:45:08,268 - algorithms.model_training - INFO - 模型 CatBoost 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\CatBoost_results.joblib
2025-08-24 09:45:08,270 - ModelTrainingManager - WARNING - 模型 LogisticRegression 不可用
2025-08-24 09:45:08,278 - algorithms.model_training - INFO - 开始训练模型: SVM
2025-08-24 09:45:08,278 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:08,278 - algorithms.model_training - WARNING - [SVM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:08,289 - algorithms.model_training - INFO - 模型 SVM 训练完成，用时 0.01秒
2025-08-24 09:45:08,290 - algorithms.model_training - INFO - 准确率: 0.8000, AUC: 0.9207161125319694
2025-08-24 09:45:08,293 - algorithms.model_training - INFO - 模型 SVM 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\SVM_results.joblib
2025-08-24 09:45:08,295 - algorithms.model_training - INFO - 开始训练模型: KNN
2025-08-24 09:45:08,295 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:08,295 - algorithms.model_training - WARNING - [KNN] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:08,305 - algorithms.model_training - INFO - 模型 KNN 训练完成，用时 0.01秒
2025-08-24 09:45:08,306 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.9322250639386189
2025-08-24 09:45:08,308 - algorithms.model_training - INFO - 模型 KNN 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\KNN_results.joblib
2025-08-24 09:45:08,311 - algorithms.model_training - INFO - 开始训练模型: NaiveBayes
2025-08-24 09:45:08,311 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:08,311 - algorithms.model_training - WARNING - [NaiveBayes] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:08,318 - algorithms.model_training - INFO - 模型 NaiveBayes 训练完成，用时 0.01秒
2025-08-24 09:45:08,318 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.89769820971867
2025-08-24 09:45:08,321 - algorithms.model_training - INFO - 模型 NaiveBayes 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\NaiveBayes_results.joblib
2025-08-24 09:45:08,335 - algorithms.model_training - INFO - 开始训练模型: NeuralNet
2025-08-24 09:45:08,336 - algorithms.model_training - INFO - 检测到问题类型: classification
2025-08-24 09:45:08,336 - algorithms.model_training - WARNING - [NeuralNet] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:45:08,583 - algorithms.model_training - INFO - 模型 NeuralNet 训练完成，用时 0.25秒
2025-08-24 09:45:08,583 - algorithms.model_training - INFO - 准确率: 0.8750, AUC: 0.959079283887468
2025-08-24 09:45:08,591 - algorithms.model_training - INFO - 模型 NeuralNet 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\NeuralNet_results.joblib
2025-08-24 09:46:15,648 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:46:15,649 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:50:57,172 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-24 09:50:58,912 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:50:59,436 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:50:59,559 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:50:59,623 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:50:59,721 - core.config_manager - INFO - 已加载配置文件: D:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:50:59,722 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:50:59,877 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:50:59,877 - __main__ - INFO - 启动GUI主循环
2025-08-24 09:52:23,024 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:52:23,025 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:53:10,537 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-24 09:53:12,257 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:53:12,705 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:53:12,814 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:53:12,875 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:53:12,968 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:53:12,968 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:53:13,115 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:53:13,116 - __main__ - INFO - 启动GUI主循环
2025-08-24 09:53:27,182 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:53:27,189 - VisualizationManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-24 09:53:31,694 - algorithms.data_preprocessing - INFO - 成功加载数据: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv, 形状: (197, 9)
2025-08-24 09:53:31,694 - algorithms.data_preprocessing - INFO - 列名清理完成，共处理 9 列
2025-08-24 09:53:31,695 - algorithms.data_preprocessing - INFO - 自动检测到目标列: label
2025-08-24 09:53:31,726 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-24 09:53:31,727 - VisualizationManager - INFO - 可视化模块已加载数据: (197, 9)
2025-08-24 09:53:31,729 - EnsembleManager - INFO - 集成学习模块已加载数据: (197, 9)
2025-08-24 09:53:31,730 - HyperparameterTuningManager - INFO - 超参数调优模块已加载数据: (197, 9)
2025-08-24 09:53:34,890 - gui.components.progress_widget - INFO - 进度开始: 正在加载数据...
2025-08-24 09:53:34,893 - utils.data_explorer - INFO - 数据基本信息分析完成，形状: (197, 9)
2025-08-24 09:53:34,893 - gui.modules.data_exploration - INFO - 数据分析完成: 197行, 9列
2025-08-24 09:53:34,900 - gui.components.progress_widget - INFO - 进度完成: 数据加载完成
2025-08-24 09:54:17,915 - algorithms.model_training - INFO - 开始训练模型: DecisionTree
2025-08-24 09:54:17,915 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:17,915 - algorithms.model_training - WARNING - [DecisionTree] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:17,919 - algorithms.model_training - INFO - 模型 DecisionTree 训练完成，用时 0.00秒
2025-08-24 09:54:17,920 - algorithms.model_training - INFO - R²: 0.3334, MSE: 6582.1953
2025-08-24 09:54:17,922 - algorithms.model_training - INFO - 模型 DecisionTree 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\DecisionTree_results.joblib
2025-08-24 09:54:17,927 - algorithms.model_training - INFO - 开始训练模型: RandomForest
2025-08-24 09:54:17,928 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:17,928 - algorithms.model_training - WARNING - [RandomForest] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:18,064 - algorithms.model_training - INFO - 模型 RandomForest 训练完成，用时 0.14秒
2025-08-24 09:54:18,064 - algorithms.model_training - INFO - R²: 0.6275, MSE: 3678.2684
2025-08-24 09:54:18,082 - algorithms.model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\RandomForest_results.joblib
2025-08-24 09:54:18,085 - algorithms.model_training - INFO - 开始训练模型: XGBoost
2025-08-24 09:54:18,085 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:18,085 - algorithms.model_training - WARNING - [XGBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:18,155 - algorithms.model_training - INFO - 模型 XGBoost 训练完成，用时 0.07秒
2025-08-24 09:54:18,157 - algorithms.model_training - INFO - R²: 0.7429, MSE: 2539.0231
2025-08-24 09:54:18,162 - algorithms.model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\XGBoost_results.joblib
2025-08-24 09:54:18,165 - algorithms.model_training - INFO - 开始训练模型: LightGBM
2025-08-24 09:54:18,166 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:18,167 - algorithms.model_training - WARNING - [LightGBM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:18,241 - algorithms.model_training - INFO - 模型 LightGBM 训练完成，用时 0.08秒
2025-08-24 09:54:18,241 - algorithms.model_training - INFO - R²: 0.6587, MSE: 3370.5299
2025-08-24 09:54:18,245 - algorithms.model_training - INFO - 模型 LightGBM 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\LightGBM_results.joblib
2025-08-24 09:54:18,248 - algorithms.model_training - INFO - 开始训练模型: CatBoost
2025-08-24 09:54:18,248 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:18,249 - algorithms.model_training - WARNING - [CatBoost] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:18,518 - algorithms.model_training - INFO - 模型 CatBoost 训练完成，用时 0.27秒
2025-08-24 09:54:18,519 - algorithms.model_training - INFO - R²: 0.6943, MSE: 3018.8005
2025-08-24 09:54:18,520 - algorithms.model_training - INFO - 模型 CatBoost 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\CatBoost_results.joblib
2025-08-24 09:54:18,522 - ModelTrainingManager - WARNING - 模型 LogisticRegression 不可用
2025-08-24 09:54:18,525 - algorithms.model_training - INFO - 开始训练模型: SVM
2025-08-24 09:54:18,525 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:18,525 - algorithms.model_training - WARNING - [SVM] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:18,525 - algorithms.model_training - ERROR - 训练模型 SVM 时出错: __init__() got an unexpected keyword argument 'random_state'
2025-08-24 09:54:18,525 - ModelTrainingManager - ERROR - 训练模型 SVM 时出错: __init__() got an unexpected keyword argument 'random_state'
2025-08-24 09:54:18,528 - algorithms.model_training - INFO - 开始训练模型: KNN
2025-08-24 09:54:18,528 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:18,528 - algorithms.model_training - WARNING - [KNN] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:18,531 - algorithms.model_training - INFO - 模型 KNN 训练完成，用时 0.00秒
2025-08-24 09:54:18,531 - algorithms.model_training - INFO - R²: 0.2557, MSE: 7349.4923
2025-08-24 09:54:18,534 - algorithms.model_training - INFO - 模型 KNN 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\KNN_results.joblib
2025-08-24 09:54:18,539 - algorithms.model_training - INFO - 开始训练模型: NaiveBayes
2025-08-24 09:54:18,539 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:18,539 - ModelTrainingManager - ERROR - 训练模型 NaiveBayes 时出错: 模型 NaiveBayes 不支持回归任务
2025-08-24 09:54:18,548 - algorithms.model_training - INFO - 开始训练模型: NeuralNet
2025-08-24 09:54:18,549 - algorithms.model_training - INFO - 检测到问题类型: regression
2025-08-24 09:54:18,549 - algorithms.model_training - WARNING - [NeuralNet] GPU配置失败: name 'get_gpu_manager' is not defined，使用CPU模式
2025-08-24 09:54:18,571 - algorithms.model_training - INFO - 模型 NeuralNet 训练完成，用时 0.02秒
2025-08-24 09:54:18,571 - algorithms.model_training - INFO - R²: -5.2127, MSE: 61345.5445
2025-08-24 09:54:18,574 - algorithms.model_training - INFO - 模型 NeuralNet 的结果已缓存到: d:\Code\MM01U\refactored_ml_platform\cache\NeuralNet_results.joblib
2025-08-24 09:54:30,736 - __main__ - INFO - 应用程序正在退出
2025-08-24 09:54:30,737 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:57:51,548 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-24 09:57:53,349 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 09:57:53,833 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 09:57:53,940 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 09:57:54,003 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 09:57:54,098 - core.config_manager - INFO - 已加载配置文件: D:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 09:57:54,099 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 09:57:54,251 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 09:57:54,251 - __main__ - INFO - 启动GUI主循环
2025-08-24 10:05:10,918 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-24 10:05:12,521 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-24 10:05:12,968 - gui.modules.data_exploration - INFO - 数据探索模块初始化完成
2025-08-24 10:05:13,071 - gui.components.progress_widget - INFO - 进度条组件初始化完成
2025-08-24 10:05:13,138 - gui.components.data_table - INFO - 数据表格组件初始化完成
2025-08-24 10:05:13,225 - core.config_manager - INFO - 已加载配置文件: d:\Code\MM01U\refactored_ml_platform\config\core_config.json
2025-08-24 10:05:13,225 - core.session_manager - INFO - 会话管理器初始化完成
2025-08-24 10:05:13,368 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-24 10:05:13,368 - __main__ - INFO - 启动GUI主循环
2025-08-24 10:05:19,641 - __main__ - INFO - 应用程序正在退出
2025-08-24 10:05:19,642 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
