#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构版多模型集成机器学习平台主程序
提供应用程序入口点和基础初始化功能
"""

import sys
import tkinter as tk
from pathlib import Path
import logging
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.environ['PYTHONPATH'] = str(project_root)


class RefactoredMLPlatform:
    """
    重构版机器学习平台主应用程序类
    负责应用程序的初始化、配置和生命周期管理
    """

    def __init__(self):
        """初始化应用程序"""
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)

        # 创建主窗口
        self.root = tk.Tk()

        # 设置项目路径
        self.project_path = project_root
        self.logger.info(f"项目路径: {self.project_path}")

        # 导入并初始化核心组件
        try:
            from gui.core.event_manager import get_event_manager
            from gui.core.config_manager import get_gui_config
            from gui.core.component_factory import get_component_factory
            from gui.layouts.main_layout import MainWindow

            self.event_manager = get_event_manager()
            self.config = get_gui_config()
            self.component_factory = get_component_factory()

            # 设置窗口
            self._setup_window()

            # 创建主界面
            self.main_window = MainWindow(self.root)

            # 绑定退出事件
            self.root.protocol("WM_DELETE_WINDOW", self._on_exit)

            self.logger.info("重构版机器学习平台初始化完成")

        except ImportError as e:
            self.logger.error(f"GUI模块导入失败: {e}")
            raise


    def _setup_logging(self):
        """设置日志系统"""
        # 确保日志目录存在
        log_dir = project_root / 'logs'
        log_dir.mkdir(exist_ok=True)

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'app.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

    def _setup_window(self):
        """设置主窗口"""
        window_config = self.config.get_window_config()

        # 设置标题
        self.root.title(window_config.get('title', '多模型集成机器学习平台 - 重构版'))

        # 设置大小和位置
        size = window_config.get('size', [1400, 900])
        min_size = window_config.get('min_size', [1200, 800])

        self.root.geometry(f"{size[0]}x{size[1]}")
        self.root.minsize(min_size[0], min_size[1])

        # 居中显示
        if window_config.get('center_on_screen', True):
            self._center_window()

        # 设置图标（如果存在）
        icon_path = self.project_path / 'assets' / 'icon.ico'
        if icon_path.exists():
            try:
                self.root.iconbitmap(str(icon_path))
            except Exception as e:
                self.logger.warning(f"无法设置应用图标: {e}")

    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()

        # 获取窗口大小
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()

        # 获取屏幕大小
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def _on_exit(self):
        """应用程序退出处理"""
        try:
            self.logger.info("应用程序正在退出")

            # 保存配置
            self.config.save_config()

            # 清理资源
            if hasattr(self, 'main_window'):
                try:
                    # 清理主窗口的定时器和资源
                    if hasattr(self.main_window, 'cleanup'):
                        self.main_window.cleanup()
                    if hasattr(self.main_window, 'destroy'):
                        self.main_window.destroy()
                except Exception as e:
                    self.logger.warning(f"销毁主窗口时出错: {e}")

            # 清理所有待处理的事件
            try:
                self.root.update_idletasks()
            except:
                pass

            # 退出应用
            self.root.quit()
            self.root.destroy()

        except Exception as e:
            self.logger.error(f"退出时发生错误: {e}")
            try:
                self.root.quit()
            except:
                pass
            try:
                self.root.destroy()
            except:
                pass

    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("启动GUI主循环")
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
            self._on_exit()
        except Exception as e:
            self.logger.error(f"应用程序运行时发生错误: {e}")
            raise


def main():
    """主函数"""
    try:
        # 创建并运行应用程序
        app = RefactoredMLPlatform()
        app.run()

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
