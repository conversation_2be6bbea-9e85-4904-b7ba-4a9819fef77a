#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学术级图表工具
提供符合学术论文发表标准的专业可视化功能
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.metrics import roc_curve, auc, precision_recall_curve
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
import warnings

from .error_handler import get_error_handler, error_handler

# 忽略matplotlib警告
warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")


class AcademicPlotManager:
    """学术级图表管理器"""
    
    def __init__(self):
        """初始化学术级图表管理器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # 设置学术级绘图样式
        self._setup_academic_style()
        
        # 学术期刊颜色配置（扩展版）
        self.journal_colors = {
            'nature': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2'],
            'science': ['#0173b2', '#de8f05', '#029e73', '#cc78bc', '#ca9161', '#fbafe4', '#949494'],
            'cell': ['#e69f00', '#56b4e9', '#009e73', '#f0e442', '#0072b2', '#d55e00', '#cc79a7'],
            'nejm': ['#1b9e77', '#d95f02', '#7570b3', '#e7298a', '#66a61e', '#e6ab02', '#a6761d'],
            'lancet': ['#00468b', '#ed0000', '#42b540', '#0099b4', '#925e9f', '#fdae61', '#abd9e9'],
            'plos': ['#1f78b4', '#33a02c', '#e31a1c', '#ff7f00', '#6a3d9a', '#b15928', '#a6cee3'],
            'bmj': ['#7fc97f', '#beaed4', '#fdc086', '#ffff99', '#386cb0', '#f0027f', '#bf5b17'],
            'jama': ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69']
        }

        # 学术级配色方案
        self.academic_palettes = {
            'qualitative': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
            'sequential': ['#08519c', '#3182bd', '#6baed6', '#9ecae1', '#c6dbef'],
            'diverging': ['#d73027', '#f46d43', '#fdae61', '#fee08b', '#e6f598', '#abdda4', '#66c2a5', '#3288bd'],
            'colorblind_safe': ['#1b9e77', '#d95f02', '#7570b3', '#e7298a', '#66a61e', '#e6ab02', '#a6761d'],
            'grayscale': ['#000000', '#404040', '#808080', '#bfbfbf', '#ffffff']
        }
        
        # 默认使用Nature风格
        self.current_palette = self.journal_colors['nature']
    
    def _setup_academic_style(self):
        """设置学术级绘图样式"""
        # 设置matplotlib参数以符合学术标准
        plt.rcParams.update({
            # 字体设置
            'font.family': 'sans-serif',
            'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],
            'font.size': 12,
            'axes.titlesize': 14,
            'axes.labelsize': 12,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': 10,
            'figure.titlesize': 16,
            
            # 图形设置
            'figure.figsize': (6, 4.5),  # 标准学术图形尺寸
            'figure.dpi': 300,
            'savefig.dpi': 300,
            'savefig.format': 'pdf',
            'savefig.bbox': 'tight',
            'savefig.pad_inches': 0.1,
            
            # 轴设置
            'axes.linewidth': 1.0,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.grid': True,
            'axes.grid.alpha': 0.3,
            'grid.linewidth': 0.5,
            
            # 线条设置
            'lines.linewidth': 1.5,
            'lines.markersize': 6,
            
            # 颜色设置
            'axes.prop_cycle': plt.cycler('color', self.current_palette)
        })
    
    @error_handler("绘制学术级相关性矩阵")
    def plot_correlation_matrix(self, data: pd.DataFrame, method: str = 'pearson',
                               figsize: Tuple[int, int] = (10, 8),
                               save_path: Optional[str] = None,
                               title: Optional[str] = None,
                               show_values: bool = True,
                               mask_upper: bool = False,
                               significance_test: bool = True,
                               journal_style: str = 'nature') -> plt.Figure:
        """
        绘制学术级相关性矩阵热力图
        
        Args:
            data: 数据框
            method: 相关性计算方法 ('pearson', 'spearman', 'kendall')
            figsize: 图形大小
            save_path: 保存路径
            title: 图表标题
            show_values: 是否显示数值
            mask_upper: 是否遮罩上三角
            significance_test: 是否进行显著性检验
            journal_style: 期刊风格
            
        Returns:
            matplotlib图形对象
        """
        # 设置期刊风格
        self._set_journal_style(journal_style)
        
        # 计算相关性矩阵
        corr_matrix = data.corr(method=method)
        
        # 显著性检验
        p_values = None
        if significance_test:
            p_values = self._calculate_correlation_pvalues(data, method)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 创建遮罩
        mask = None
        if mask_upper:
            mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        
        # 绘制热力图
        sns.heatmap(corr_matrix, 
                   mask=mask,
                   annot=show_values,
                   fmt='.3f',
                   cmap='RdBu_r',
                   center=0,
                   square=True,
                   linewidths=0.5,
                   cbar_kws={'shrink': 0.8, 'label': f'{method.capitalize()} Correlation'},
                   ax=ax)
        
        # 添加显著性标记
        if significance_test and p_values is not None:
            self._add_significance_markers(ax, corr_matrix, p_values, mask)
        
        # 设置标题和标签
        if title:
            ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
        else:
            ax.set_title(f'{method.capitalize()} Correlation Matrix', 
                        fontsize=14, fontweight='bold', pad=20)
        
        # 旋转标签
        ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')
        ax.set_yticklabels(ax.get_yticklabels(), rotation=0)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_path:
            self._save_academic_figure(fig, save_path)
        
        return fig
    
    def _calculate_correlation_pvalues(self, data: pd.DataFrame, method: str) -> pd.DataFrame:
        """计算相关性p值"""
        n_vars = data.shape[1]
        p_matrix = np.zeros((n_vars, n_vars))
        
        for i in range(n_vars):
            for j in range(n_vars):
                if i == j:
                    p_matrix[i, j] = 0
                else:
                    if method == 'pearson':
                        _, p_val = stats.pearsonr(data.iloc[:, i], data.iloc[:, j])
                    elif method == 'spearman':
                        _, p_val = stats.spearmanr(data.iloc[:, i], data.iloc[:, j])
                    elif method == 'kendall':
                        _, p_val = stats.kendalltau(data.iloc[:, i], data.iloc[:, j])
                    else:
                        p_val = 1.0
                    
                    p_matrix[i, j] = p_val
        
        return pd.DataFrame(p_matrix, index=data.columns, columns=data.columns)
    
    def _add_significance_markers(self, ax, corr_matrix, p_values, mask):
        """添加显著性标记"""
        for i in range(len(corr_matrix.index)):
            for j in range(len(corr_matrix.columns)):
                if mask is not None and mask[i, j]:
                    continue
                
                p_val = p_values.iloc[i, j]
                
                # 添加显著性星号
                if p_val < 0.001:
                    marker = '***'
                elif p_val < 0.01:
                    marker = '**'
                elif p_val < 0.05:
                    marker = '*'
                else:
                    marker = ''
                
                if marker:
                    ax.text(j + 0.5, i + 0.7, marker, 
                           ha='center', va='center', 
                           fontsize=8, fontweight='bold', color='white')
    
    @error_handler("绘制学术级ROC曲线")
    def plot_academic_roc_curves(self, model_results: Dict[str, Dict[str, Any]],
                                figsize: Tuple[int, int] = (6, 6),
                                save_path: Optional[str] = None,
                                title: Optional[str] = None,
                                journal_style: str = 'nature',
                                show_ci: bool = True) -> plt.Figure:
        """
        绘制学术级多模型ROC曲线对比
        
        Args:
            model_results: 模型结果字典
            figsize: 图形大小
            save_path: 保存路径
            title: 图表标题
            journal_style: 期刊风格
            show_ci: 是否显示置信区间
            
        Returns:
            matplotlib图形对象
        """
        # 设置期刊风格
        self._set_journal_style(journal_style)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制每个模型的ROC曲线
        colors = self.journal_colors[journal_style]
        
        for i, (model_name, result) in enumerate(model_results.items()):
            y_true = result.get('y_true') or result.get('y_test')
            y_pred_proba = result.get('y_pred_proba')
            
            if y_true is None or y_pred_proba is None:
                continue
            
            # 计算ROC曲线
            fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
            roc_auc = auc(fpr, tpr)
            
            color = colors[i % len(colors)]
            
            # 绘制ROC曲线
            ax.plot(fpr, tpr, color=color, linewidth=2,
                   label=f'{model_name} (AUC = {roc_auc:.3f})')
            
            # 添加置信区间（如果需要）
            if show_ci:
                # 这里可以添加bootstrap置信区间的计算
                pass
        
        # 绘制对角线
        ax.plot([0, 1], [0, 1], 'k--', linewidth=1, alpha=0.8, label='Random')
        
        # 设置轴属性
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.0])
        ax.set_xlabel('False Positive Rate', fontsize=12)
        ax.set_ylabel('True Positive Rate', fontsize=12)
        
        # 设置标题
        if title:
            ax.set_title(title, fontsize=14, fontweight='bold')
        else:
            ax.set_title('Receiver Operating Characteristic', fontsize=14, fontweight='bold')
        
        # 设置图例
        ax.legend(loc='lower right', frameon=True, fancybox=True, shadow=True)
        
        # 设置网格
        ax.grid(True, alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_path:
            self._save_academic_figure(fig, save_path)
        
        return fig
    
    @error_handler("绘制学术级森林图")
    def plot_forest_plot(self, data: pd.DataFrame, 
                        effect_col: str, ci_lower_col: str, ci_upper_col: str,
                        label_col: str, figsize: Tuple[int, int] = (8, 6),
                        save_path: Optional[str] = None,
                        title: Optional[str] = None,
                        journal_style: str = 'nature') -> plt.Figure:
        """
        绘制学术级森林图
        
        Args:
            data: 数据框
            effect_col: 效应值列名
            ci_lower_col: 置信区间下限列名
            ci_upper_col: 置信区间上限列名
            label_col: 标签列名
            figsize: 图形大小
            save_path: 保存路径
            title: 图表标题
            journal_style: 期刊风格
            
        Returns:
            matplotlib图形对象
        """
        # 设置期刊风格
        self._set_journal_style(journal_style)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 获取数据
        effects = data[effect_col].values
        ci_lower = data[ci_lower_col].values
        ci_upper = data[ci_upper_col].values
        labels = data[label_col].values
        
        y_positions = np.arange(len(labels))
        
        # 绘制置信区间
        for i, (effect, lower, upper) in enumerate(zip(effects, ci_lower, ci_upper)):
            # 绘制置信区间线
            ax.plot([lower, upper], [i, i], 'k-', linewidth=2, alpha=0.8)
            
            # 绘制效应值点
            ax.plot(effect, i, 'o', markersize=8, color=self.current_palette[0], 
                   markeredgecolor='black', markeredgewidth=1)
            
            # 绘制置信区间端点
            ax.plot([lower, lower], [i-0.1, i+0.1], 'k-', linewidth=2)
            ax.plot([upper, upper], [i-0.1, i+0.1], 'k-', linewidth=2)
        
        # 绘制无效应线
        ax.axvline(x=0, color='red', linestyle='--', linewidth=1, alpha=0.8)
        
        # 设置y轴
        ax.set_yticks(y_positions)
        ax.set_yticklabels(labels)
        ax.invert_yaxis()
        
        # 设置x轴
        ax.set_xlabel('Effect Size', fontsize=12)
        
        # 设置标题
        if title:
            ax.set_title(title, fontsize=14, fontweight='bold')
        else:
            ax.set_title('Forest Plot', fontsize=14, fontweight='bold')
        
        # 设置网格
        ax.grid(True, alpha=0.3, axis='x')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_path:
            self._save_academic_figure(fig, save_path)
        
        return fig
    
    @error_handler("绘制学术级小提琴图")
    def plot_academic_violin_plot(self, data: pd.DataFrame, x_col: str, y_col: str,
                                 hue_col: Optional[str] = None,
                                 figsize: Tuple[int, int] = (8, 6),
                                 save_path: Optional[str] = None,
                                 title: Optional[str] = None,
                                 journal_style: str = 'nature') -> plt.Figure:
        """
        绘制学术级小提琴图
        
        Args:
            data: 数据框
            x_col: x轴列名
            y_col: y轴列名
            hue_col: 分组列名
            figsize: 图形大小
            save_path: 保存路径
            title: 图表标题
            journal_style: 期刊风格
            
        Returns:
            matplotlib图形对象
        """
        # 设置期刊风格
        self._set_journal_style(journal_style)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制小提琴图
        sns.violinplot(data=data, x=x_col, y=y_col, hue=hue_col,
                      palette=self.journal_colors[journal_style],
                      ax=ax, inner='box')
        
        # 设置标题和标签
        if title:
            ax.set_title(title, fontsize=14, fontweight='bold')
        
        ax.set_xlabel(x_col.replace('_', ' ').title(), fontsize=12)
        ax.set_ylabel(y_col.replace('_', ' ').title(), fontsize=12)
        
        # 旋转x轴标签（如果需要）
        if len(data[x_col].unique()) > 5:
            ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_path:
            self._save_academic_figure(fig, save_path)
        
        return fig
    
    def _set_journal_style(self, journal_style: str):
        """设置期刊风格"""
        if journal_style in self.journal_colors:
            self.current_palette = self.journal_colors[journal_style]
            plt.rcParams['axes.prop_cycle'] = plt.cycler('color', self.current_palette)
    
    def _save_academic_figure(self, fig: plt.Figure, save_path: str, 
                            formats: List[str] = ['pdf', 'png', 'svg']):
        """
        保存学术级图形
        
        Args:
            fig: matplotlib图形对象
            save_path: 保存路径（不含扩展名）
            formats: 保存格式列表
        """
        save_path = Path(save_path)
        
        for fmt in formats:
            output_path = save_path.with_suffix(f'.{fmt}')
            
            # 设置不同格式的参数
            if fmt == 'pdf':
                fig.savefig(output_path, format='pdf', dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
            elif fmt == 'png':
                fig.savefig(output_path, format='png', dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
            elif fmt == 'svg':
                fig.savefig(output_path, format='svg', bbox_inches='tight',
                           facecolor='white', edgecolor='none')
            
            self.logger.info(f"学术级图表已保存: {output_path}")
    
    def create_publication_ready_figure(self, width_inches: float = 6.0, 
                                      height_inches: float = 4.5,
                                      journal_style: str = 'nature') -> Tuple[plt.Figure, plt.Axes]:
        """
        创建符合发表标准的图形
        
        Args:
            width_inches: 宽度（英寸）
            height_inches: 高度（英寸）
            journal_style: 期刊风格
            
        Returns:
            图形和轴对象
        """
        self._set_journal_style(journal_style)
        
        fig, ax = plt.subplots(figsize=(width_inches, height_inches))
        
        # 设置学术标准的轴属性
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1)
        ax.spines['bottom'].set_linewidth(1)
        
        return fig, ax
    
    def get_journal_requirements(self, journal: str) -> Dict[str, Any]:
        """
        获取期刊图表要求
        
        Args:
            journal: 期刊名称
            
        Returns:
            期刊要求字典
        """
        requirements = {
            'nature': {
                'max_width_inches': 7.0,
                'max_height_inches': 9.0,
                'min_font_size': 8,
                'preferred_formats': ['pdf', 'eps'],
                'dpi': 300,
                'color_palette': self.journal_colors['nature']
            },
            'science': {
                'max_width_inches': 6.5,
                'max_height_inches': 8.5,
                'min_font_size': 8,
                'preferred_formats': ['pdf', 'eps'],
                'dpi': 300,
                'color_palette': self.journal_colors['science']
            },
            'cell': {
                'max_width_inches': 7.0,
                'max_height_inches': 9.0,
                'min_font_size': 8,
                'preferred_formats': ['pdf', 'tiff'],
                'dpi': 300,
                'color_palette': self.journal_colors['cell']
            }
        }

        return requirements.get(journal, requirements['nature'])

    @error_handler("绘制学术级森林图")
    def plot_forest_plot(self, data: pd.DataFrame, effect_col: str, ci_lower_col: str,
                        ci_upper_col: str, label_col: str,
                        figsize: Tuple[int, int] = (8, 6),
                        save_path: Optional[str] = None,
                        title: Optional[str] = None,
                        journal_style: str = 'nature') -> plt.Figure:
        """
        绘制学术级森林图

        Args:
            data: 数据框
            effect_col: 效应值列名
            ci_lower_col: 置信区间下界列名
            ci_upper_col: 置信区间上界列名
            label_col: 标签列名
            figsize: 图形大小
            save_path: 保存路径
            title: 图表标题
            journal_style: 期刊风格

        Returns:
            matplotlib图形对象
        """
        # 设置期刊风格
        self._set_journal_style(journal_style)

        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)

        # 准备数据
        y_pos = np.arange(len(data))
        effects = data[effect_col].values
        ci_lower = data[ci_lower_col].values
        ci_upper = data[ci_upper_col].values
        labels = data[label_col].values

        # 绘制置信区间
        for i, (effect, lower, upper) in enumerate(zip(effects, ci_lower, ci_upper)):
            ax.plot([lower, upper], [i, i], 'k-', linewidth=2, alpha=0.7)
            ax.plot([lower, lower], [i-0.1, i+0.1], 'k-', linewidth=2)
            ax.plot([upper, upper], [i-0.1, i+0.1], 'k-', linewidth=2)

        # 绘制效应值点
        colors = self.journal_colors[journal_style]
        ax.scatter(effects, y_pos, s=100, c=colors[0], alpha=0.8, zorder=5)

        # 添加无效应线
        ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, linewidth=1)

        # 设置标签
        ax.set_yticks(y_pos)
        ax.set_yticklabels(labels)
        ax.set_xlabel('Effect Size', fontsize=12, fontweight='bold')

        if title:
            ax.set_title(title, fontsize=14, fontweight='bold')

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.grid(True, alpha=0.3, axis='x')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"森林图已保存到: {save_path}")

        return fig

    @error_handler("绘制学术级热力图")
    def plot_enhanced_heatmap(self, data: pd.DataFrame,
                             figsize: Tuple[int, int] = (10, 8),
                             save_path: Optional[str] = None,
                             title: Optional[str] = None,
                             journal_style: str = 'nature',
                             show_values: bool = True,
                             cmap: str = 'RdBu_r') -> plt.Figure:
        """
        绘制增强版学术级热力图

        Args:
            data: 数据框
            figsize: 图形大小
            save_path: 保存路径
            title: 图表标题
            journal_style: 期刊风格
            show_values: 是否显示数值
            cmap: 颜色映射

        Returns:
            matplotlib图形对象
        """
        # 设置期刊风格
        self._set_journal_style(journal_style)

        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)

        # 绘制热力图
        sns.heatmap(data, annot=show_values, cmap=cmap, center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, ax=ax)

        # 设置标题
        if title:
            ax.set_title(title, fontsize=14, fontweight='bold', pad=20)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"增强版热力图已保存到: {save_path}")

        return fig


# 全局学术级图表管理器实例
_academic_plot_manager = None

def get_academic_plot_manager() -> AcademicPlotManager:
    """
    获取全局学术级图表管理器实例
    
    Returns:
        学术级图表管理器实例
    """
    global _academic_plot_manager
    if _academic_plot_manager is None:
        _academic_plot_manager = AcademicPlotManager()
    return _academic_plot_manager


# 便捷函数
def create_correlation_heatmap(data: pd.DataFrame, save_path: str, 
                             journal_style: str = 'nature', **kwargs) -> plt.Figure:
    """
    创建相关性热力图的便捷函数
    
    Args:
        data: 数据框
        save_path: 保存路径
        journal_style: 期刊风格
        **kwargs: 其他参数
        
    Returns:
        matplotlib图形对象
    """
    manager = get_academic_plot_manager()
    return manager.plot_correlation_matrix(data, save_path=save_path, 
                                         journal_style=journal_style, **kwargs)


def create_academic_roc_plot(model_results: Dict[str, Dict[str, Any]],
                           save_path: str, journal_style: str = 'nature',
                           **kwargs) -> plt.Figure:
    """
    创建学术级ROC图的便捷函数

    Args:
        model_results: 模型结果字典
        save_path: 保存路径
        journal_style: 期刊风格
        **kwargs: 其他参数

    Returns:
        matplotlib图形对象
    """
    manager = get_academic_plot_manager()
    return manager.plot_academic_roc_curves(model_results, save_path=save_path,
                                          journal_style=journal_style, **kwargs)


def setup_academic_matplotlib():
    """设置matplotlib为学术标准"""
    manager = get_academic_plot_manager()
    manager._setup_academic_style()
