#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索工具
提供数据探索和统计分析功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from .error_handler import get_error_handler, error_handler


class DataExplorer:
    """数据探索器"""
    
    def __init__(self):
        """初始化数据探索器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # 设置matplotlib字体（避免中文字体问题）
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
    
    @error_handler("数据基本信息分析")
    def analyze_basic_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据基本信息
        
        Args:
            df: 数据框
            
        Returns:
            基本信息字典
        """
        info = {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': df.dtypes.to_dict(),
            'memory_usage': df.memory_usage(deep=True).sum() / 1024**2,  # MB
            'missing_values': df.isnull().sum().to_dict(),
            'missing_percentage': (df.isnull().sum() / len(df) * 100).to_dict(),
            'duplicates': df.duplicated().sum(),
            'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': df.select_dtypes(include=['object', 'category']).columns.tolist()
        }
        
        self.logger.info(f"数据基本信息分析完成，形状: {info['shape']}")
        return info
    
    @error_handler("描述性统计分析")
    def analyze_descriptive_stats(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        分析描述性统计
        
        Args:
            df: 数据框
            
        Returns:
            描述性统计结果
        """
        results = {}
        
        # 数值变量描述性统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            results['numeric'] = df[numeric_cols].describe()
            
            # 添加偏度和峰度
            skewness = df[numeric_cols].skew()
            kurtosis = df[numeric_cols].kurtosis()
            
            results['numeric'].loc['skewness'] = skewness
            results['numeric'].loc['kurtosis'] = kurtosis
        
        # 分类变量描述性统计
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            cat_stats = []
            for col in categorical_cols:
                stats_dict = {
                    'column': col,
                    'unique_count': df[col].nunique(),
                    'most_frequent': df[col].mode().iloc[0] if not df[col].mode().empty else None,
                    'most_frequent_count': df[col].value_counts().iloc[0] if len(df[col].value_counts()) > 0 else 0,
                    'missing_count': df[col].isnull().sum()
                }
                cat_stats.append(stats_dict)
            
            results['categorical'] = pd.DataFrame(cat_stats)
        
        self.logger.info("描述性统计分析完成")
        return results
    
    @error_handler("相关性分析")
    def analyze_correlation(self, df: pd.DataFrame, method: str = 'pearson') -> pd.DataFrame:
        """
        分析变量间相关性
        
        Args:
            df: 数据框
            method: 相关性方法 ('pearson', 'spearman', 'kendall')
            
        Returns:
            相关性矩阵
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) < 2:
            self.logger.warning("数值变量少于2个，无法进行相关性分析")
            return pd.DataFrame()
        
        correlation_matrix = df[numeric_cols].corr(method=method)
        
        self.logger.info(f"相关性分析完成，方法: {method}")
        return correlation_matrix
    
    @error_handler("分组概率分析")
    def analyze_group_probabilities(self, df: pd.DataFrame, target_col: str, 
                                  group_cols: List[str], bins: int = 5) -> Dict[str, pd.DataFrame]:
        """
        分析分组概率
        
        Args:
            df: 数据框
            target_col: 目标变量列名
            group_cols: 分组变量列名列表
            bins: 连续变量分箱数量
            
        Returns:
            分组概率分析结果
        """
        results = {}
        
        for col in group_cols:
            if col not in df.columns:
                continue
            
            try:
                # 处理连续变量
                if df[col].dtype in ['int64', 'float64']:
                    # 分箱处理
                    df_temp = df.copy()
                    df_temp[f'{col}_binned'] = pd.cut(df_temp[col], bins=bins, duplicates='drop')
                    group_col = f'{col}_binned'
                else:
                    df_temp = df.copy()
                    group_col = col
                
                # 计算分组概率
                crosstab = pd.crosstab(df_temp[group_col], df_temp[target_col], normalize='index')
                
                # 添加计数信息
                counts = pd.crosstab(df_temp[group_col], df_temp[target_col])
                
                # 合并概率和计数
                result_df = pd.DataFrame()
                for target_val in crosstab.columns:
                    result_df[f'prob_{target_val}'] = crosstab[target_val]
                    result_df[f'count_{target_val}'] = counts[target_val]
                
                result_df['total_count'] = counts.sum(axis=1)
                
                results[col] = result_df
                
            except Exception as e:
                self.logger.warning(f"分析变量 {col} 的分组概率时出错: {e}")
                continue
        
        self.logger.info(f"分组概率分析完成，分析了 {len(results)} 个变量")
        return results
    
    @error_handler("卡方检验")
    def perform_chi_square_test(self, df: pd.DataFrame, col1: str, col2: str) -> Dict[str, Any]:
        """
        执行卡方检验
        
        Args:
            df: 数据框
            col1: 第一个变量
            col2: 第二个变量
            
        Returns:
            卡方检验结果
        """
        # 创建交叉表
        crosstab = pd.crosstab(df[col1], df[col2])
        
        # 执行卡方检验
        chi2, p_value, dof, expected = chi2_contingency(crosstab)
        
        # 计算Cramer's V
        n = crosstab.sum().sum()
        cramers_v = np.sqrt(chi2 / (n * (min(crosstab.shape) - 1)))
        
        result = {
            'chi2_statistic': chi2,
            'p_value': p_value,
            'degrees_of_freedom': dof,
            'cramers_v': cramers_v,
            'crosstab': crosstab,
            'expected_frequencies': pd.DataFrame(expected, 
                                               index=crosstab.index, 
                                               columns=crosstab.columns)
        }
        
        self.logger.info(f"卡方检验完成: {col1} vs {col2}, p-value: {p_value:.4f}")
        return result
    
    @error_handler("创建相关性热力图")
    def create_correlation_heatmap(self, correlation_matrix: pd.DataFrame, 
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        创建相关性热力图
        
        Args:
            correlation_matrix: 相关性矩阵
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 创建热力图
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                   center=0, square=True, linewidths=0.5, ax=ax)
        
        plt.title('Feature Correlation Matrix', fontsize=16, pad=20)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"相关性热力图已保存到: {save_path}")
        
        return fig
    
    @error_handler("创建分布图")
    def create_distribution_plot(self, df: pd.DataFrame, column: str, 
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        创建变量分布图
        
        Args:
            df: 数据框
            column: 列名
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 直方图
        axes[0, 0].hist(df[column].dropna(), bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title(f'{column} - 直方图')
        axes[0, 0].set_xlabel(column)
        axes[0, 0].set_ylabel('频数')
        
        # 箱线图
        axes[0, 1].boxplot(df[column].dropna())
        axes[0, 1].set_title(f'{column} - 箱线图')
        axes[0, 1].set_ylabel(column)
        
        # Q-Q图
        stats.probplot(df[column].dropna(), dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title(f'{column} - Q-Q图')
        
        # 密度图
        df[column].dropna().plot.density(ax=axes[1, 1], color='orange')
        axes[1, 1].set_title(f'{column} - 密度图')
        axes[1, 1].set_xlabel(column)
        axes[1, 1].set_ylabel('密度')
        
        plt.suptitle(f'{column} 分布分析', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"分布图已保存到: {save_path}")
        
        return fig
    
    @error_handler("创建分组概率图")
    def create_group_probability_plot(self, group_prob_result: pd.DataFrame, 
                                    variable_name: str, target_name: str,
                                    save_path: Optional[str] = None) -> plt.Figure:
        """
        创建分组概率图
        
        Args:
            group_prob_result: 分组概率结果
            variable_name: 变量名
            target_name: 目标变量名
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 概率图
        prob_cols = [col for col in group_prob_result.columns if col.startswith('prob_')]
        group_prob_result[prob_cols].plot(kind='bar', ax=ax1, width=0.8)
        ax1.set_title(f'{variable_name} 各组的 {target_name} 概率分布')
        ax1.set_xlabel(variable_name)
        ax1.set_ylabel('概率')
        ax1.legend(title=target_name)
        ax1.tick_params(axis='x', rotation=45)
        
        # 计数图
        count_cols = [col for col in group_prob_result.columns if col.startswith('count_')]
        group_prob_result[count_cols].plot(kind='bar', ax=ax2, width=0.8)
        ax2.set_title(f'{variable_name} 各组的 {target_name} 计数分布')
        ax2.set_xlabel(variable_name)
        ax2.set_ylabel('计数')
        ax2.legend(title=target_name)
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"分组概率图已保存到: {save_path}")
        
        return fig

    @error_handler("分组概率分析（高级）")
    def create_binned_probability_analysis(self, df: pd.DataFrame,
                                         continuous_vars: List[str],
                                         target_var: str,
                                         n_bins: int = 5,
                                         binning_method: str = 'qcut',
                                         min_bin_size: int = 20) -> Dict[str, Any]:
        """
        创建分组概率分析（复刻原项目功能）

        Args:
            df: 数据框
            continuous_vars: 连续变量列表
            target_var: 目标变量名
            n_bins: 分箱数量
            binning_method: 分箱方法 ('qcut', 'cut', 'tree')
            min_bin_size: 最小分箱样本数

        Returns:
            分析结果字典
        """
        results = {}

        for var in continuous_vars:
            if var not in df.columns or var == target_var:
                continue

            try:
                var_result = self._analyze_single_variable(
                    df, var, target_var, n_bins, binning_method, min_bin_size
                )

                if var_result is not None:
                    results[var] = var_result

            except Exception as e:
                self.logger.warning(f"分析变量 {var} 时出错: {e}")
                continue

        self.logger.info(f"分组概率分析完成，成功分析 {len(results)} 个变量")
        return results

    def _analyze_single_variable(self, df: pd.DataFrame, var: str, target_var: str,
                               n_bins: int, binning_method: str, min_bin_size: int) -> Optional[Dict[str, Any]]:
        """分析单个变量的分组概率（增强版）"""
        try:
            # 确保目标变量为二值
            target_values = df[target_var].dropna().unique()
            if len(target_values) != 2 or not all(v in [0, 1] for v in target_values):
                self.logger.warning(f"目标变量 {target_var} 不是二值变量 (0/1)，尝试转换")
                # 简单的二值转换
                unique_vals = sorted(df[target_var].dropna().unique())
                if len(unique_vals) == 2:
                    df = df.copy()
                    df[target_var] = df[target_var].map({unique_vals[0]: 0, unique_vals[1]: 1})
                else:
                    self.logger.error(f"无法将目标变量 {target_var} 转换为二值变量")
                    return None

            # 处理缺失值 - 分别处理有缺失和无缺失的数据
            missing_df = df[df[var].isna() & df[target_var].notna()]
            clean_df = df[[var, target_var]].dropna()

            if len(clean_df) == 0:
                self.logger.warning(f"变量 {var} 清理后无数据")
                return None

            # 执行分箱
            bins, bin_edges, actual_bins = self._perform_binning(
                clean_df[var], clean_df[target_var], n_bins, binning_method, min_bin_size
            )

            # 计算每组的统计信息
            grouped = clean_df.groupby(bins)[target_var].agg([
                'count', 'sum', 'mean'
            ]).reset_index()

            # 重命名列
            grouped.columns = ['bin', 'total_count', 'positive_count', 'probability']

            # 添加Laplace平滑的概率
            grouped['probability_smoothed'] = (grouped['positive_count'] + 1) / (grouped['total_count'] + 2)

            # 添加区间标签
            grouped['bin_label'] = grouped['bin'].astype(str)
            grouped['bin_center'] = grouped['bin'].apply(lambda x: x.mid if hasattr(x, 'mid') else x)

            # 计算Wilson置信区间
            grouped['ci_lower'], grouped['ci_upper'] = zip(*grouped.apply(
                lambda row: self._calculate_wilson_confidence_interval(
                    row['positive_count'], row['total_count']
                ), axis=1
            ))

            # 处理缺失值组（如果存在）
            missing_result = None
            if len(missing_df) > 0:
                missing_positive = missing_df[target_var].sum()
                missing_total = len(missing_df)
                missing_prob = missing_positive / missing_total
                missing_ci_lower, missing_ci_upper = self._calculate_wilson_confidence_interval(
                    missing_positive, missing_total
                )
                missing_result = {
                    'bin_label': 'Missing',
                    'total_count': missing_total,
                    'positive_count': missing_positive,
                    'probability': missing_prob,
                    'probability_smoothed': (missing_positive + 1) / (missing_total + 2),
                    'ci_lower': missing_ci_lower,
                    'ci_upper': missing_ci_upper
                }

            return {
                'variable': var,
                'target': target_var,
                'binning_method': binning_method,
                'requested_bins': n_bins,
                'actual_bins': actual_bins,
                'n_bins': len(grouped),
                'bin_edges': bin_edges,
                'grouped_data': grouped,
                'missing_data': missing_result,
                'total_samples': len(clean_df),
                'total_samples_with_missing': len(df.dropna(subset=[target_var])),
                'overall_probability': clean_df[target_var].mean(),
                'min_bin_size': min_bin_size
            }

        except Exception as e:
            self.logger.error(f"分析变量 {var} 时出错: {e}")
            return None

    @error_handler("创建分组概率柱状图")
    def create_probability_bar_chart(self, analysis_result: Dict[str, Any],
                                   save_path: Optional[str] = None) -> plt.Figure:
        """创建分组概率柱状图"""
        if not analysis_result or 'results' not in analysis_result:
            return None

        results = analysis_result['results']
        var_name = analysis_result['variable']
        target_name = analysis_result['target']

        fig, ax = plt.subplots(figsize=(12, 8))

        # 准备数据
        groups = [r['interval_str'] for r in results]
        probabilities = [r['probability'] for r in results]
        ci_lower = [r['ci_lower'] for r in results]
        ci_upper = [r['ci_upper'] for r in results]
        counts = [r['count'] for r in results]

        # 创建柱状图
        bars = ax.bar(range(len(groups)), probabilities,
                     color='#2E86AB', alpha=0.7, edgecolor='black', linewidth=1)

        # 添加误差线（置信区间）
        yerr_lower = [max(0, p - ci_l) for p, ci_l in zip(probabilities, ci_lower)]
        yerr_upper = [max(0, ci_u - p) for p, ci_u in zip(ci_upper, probabilities)]
        ax.errorbar(range(len(groups)), probabilities,
                   yerr=[yerr_lower, yerr_upper],
                   fmt='none', color='black', capsize=5, capthick=2)

        # 在柱子上添加数值标签
        for i, (bar, prob, count) in enumerate(zip(bars, probabilities, counts)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{prob:.3f}\n(n={count})',
                   ha='center', va='bottom', fontsize=10, fontweight='bold')

        # 设置标签和标题（使用英文避免字体问题）
        ax.set_xlabel(f'{var_name} (Binned)', fontsize=12, fontweight='bold')
        ax.set_ylabel(f'Probability of {target_name}=1', fontsize=12, fontweight='bold')
        ax.set_title(f'Probability Analysis: {var_name} vs {target_name}',
                    fontsize=14, fontweight='bold', pad=20)

        # 设置x轴标签
        ax.set_xticks(range(len(groups)))
        ax.set_xticklabels(groups, rotation=45, ha='right')

        # 设置y轴范围
        ax.set_ylim(0, 1.1)

        # 添加网格
        ax.grid(True, alpha=0.3, axis='y')

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"概率柱状图已保存到: {save_path}")

        return fig

    @error_handler("创建综合比较图表")
    def create_comprehensive_comparison(self, results: Dict[str, Dict[str, Any]],
                                      target_name: str, chart_type: str = 'bar',
                                      save_path: Optional[str] = None) -> plt.Figure:
        """
        创建综合比较图表

        Args:
            results: 分析结果字典
            target_name: 目标变量名
            chart_type: 图表类型 ('bar' 或 'line')
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        n_vars = len(results)
        if n_vars == 0:
            return None

        # 计算子图布局
        n_cols = min(3, n_vars)
        n_rows = (n_vars + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))

        # 确保axes始终是二维数组
        if n_vars == 1:
            axes = np.array([[axes]])
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        elif n_cols == 1:
            axes = axes.reshape(-1, 1)

        # 颜色配置
        colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6C757D', '#28A745', '#17A2B8', '#FFC107']

        for idx, (var_name, var_result) in enumerate(results.items()):
            row = idx // n_cols
            col = idx % n_cols
            ax = axes[row, col]

            grouped_data = var_result['grouped_data']
            x_pos = np.arange(len(grouped_data))
            color = colors[idx % len(colors)]

            if chart_type == 'bar':
                # 绘制条形图
                bars = ax.bar(x_pos, grouped_data['probability'],
                             color=color, alpha=0.8, edgecolor='white', linewidth=1)

                # 添加数值标签
                for bar, prob in zip(bars, grouped_data['probability']):
                    height = bar.get_height()
                    if np.isfinite(height) and np.isfinite(prob):
                        ax.text(bar.get_x() + bar.get_width()/2., height + 0.015,
                               f'{prob:.2f}', ha='center', va='bottom',
                               fontsize=8, fontweight='normal')

            else:  # line chart
                # 绘制折线图
                probabilities = grouped_data['probability'].values
                ci_lower = grouped_data['ci_lower'].values
                ci_upper = grouped_data['ci_upper'].values

                ax.plot(x_pos, probabilities, 'o-', color=color, linewidth=2,
                       markersize=6, label=f'{var_name}')

                # 添加置信区间
                valid = np.isfinite(ci_lower) & np.isfinite(ci_upper) & np.isfinite(probabilities)
                if np.any(valid):
                    ax.fill_between(x_pos[valid], ci_lower[valid], ci_upper[valid],
                                   color=color, alpha=0.2)

                # 添加数值标签
                for i, prob in enumerate(probabilities):
                    if np.isfinite(prob):
                        ax.text(i, prob + 0.02, f'{prob:.2f}', ha='center', va='bottom',
                               fontsize=8, bbox=dict(facecolor='white', alpha=0.6,
                                                    edgecolor='none', pad=0.5))

            # 添加总体概率参考线
            overall_prob = var_result['overall_probability']
            ax.axhline(y=overall_prob, color='red', linestyle='--',
                      linewidth=2, alpha=0.7, label=f'Overall ({overall_prob:.3f})')

            # 设置标签和标题
            ax.set_title(f'{var_name}', fontsize=12, fontweight='bold')
            ax.set_xlabel('Groups', fontsize=10)
            ax.set_ylabel(f'{target_name} Probability', fontsize=10)
            ax.set_xticks(x_pos)
            ax.set_xticklabels([f'G{i+1}' for i in range(len(grouped_data))])
            ax.grid(True, alpha=0.3)

            if chart_type == 'line':
                ax.legend(fontsize=8)

            # 调整y轴范围
            max_prob = np.max(grouped_data['probability'])
            ax.set_ylim(0, max(max_prob + 0.1, overall_prob + 0.1))

        # 隐藏多余的子图
        for idx in range(n_vars, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if n_rows > 1:
                axes[row, col].set_visible(False)
            else:
                axes[col].set_visible(False)

        plt.suptitle(f'Comprehensive Probability Analysis: {target_name} ({chart_type.title()})',
                    fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"综合比较图表已保存到: {save_path}")

        return fig

    @error_handler("创建分组概率折线图")
    def create_probability_line_chart(self, analysis_result: Dict[str, Any],
                                    save_path: Optional[str] = None) -> plt.Figure:
        """创建分组概率折线图"""
        if not analysis_result or 'results' not in analysis_result:
            return None

        results = analysis_result['results']
        var_name = analysis_result['variable']
        target_name = analysis_result['target']

        fig, ax = plt.subplots(figsize=(12, 8))

        # 准备数据
        x_values = [r['mean_value'] for r in results]
        probabilities = [r['probability'] for r in results]
        ci_lower = [r['ci_lower'] for r in results]
        ci_upper = [r['ci_upper'] for r in results]

        # 创建折线图
        line = ax.plot(x_values, probabilities, 'o-',
                      color='#2E86AB', linewidth=2, markersize=8,
                      markerfacecolor='white', markeredgecolor='#2E86AB',
                      markeredgewidth=2, label=f'{target_name}=1 Probability')

        # 添加置信区间
        ax.fill_between(x_values, ci_lower, ci_upper,
                       alpha=0.3, color='#2E86AB', label='95% CI')

        # 添加数据点标签
        for x, y, count in zip(x_values, probabilities, [r['count'] for r in results]):
            ax.annotate(f'{y:.3f}\n(n={count})',
                       (x, y), textcoords="offset points",
                       xytext=(0,10), ha='center', fontsize=9)

        # 设置标签和标题
        ax.set_xlabel(f'{var_name} (Mean Value)', fontsize=12, fontweight='bold')
        ax.set_ylabel(f'Probability of {target_name}=1', fontsize=12, fontweight='bold')
        ax.set_title(f'Probability Trend: {var_name} vs {target_name}',
                    fontsize=14, fontweight='bold', pad=20)

        # 设置y轴范围
        ax.set_ylim(0, 1.1)

        # 添加图例
        ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)

        # 添加网格
        ax.grid(True, alpha=0.3)

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"概率折线图已保存到: {save_path}")

        return fig

    @error_handler("创建小提琴图")
    def create_violin_plot(self, df: pd.DataFrame, x_col: str, y_col: str,
                          hue_col: Optional[str] = None, save_path: Optional[str] = None) -> plt.Figure:
        """
        创建小提琴图

        Args:
            df: 数据框
            x_col: x轴列名
            y_col: y轴列名
            hue_col: 分组列名
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 6))

        # 绘制小提琴图
        sns.violinplot(data=df, x=x_col, y=y_col, hue=hue_col, ax=ax, inner='box')

        # 设置标题和标签
        ax.set_title(f'{y_col} Distribution by {x_col}', fontsize=14, fontweight='bold')
        ax.set_xlabel(x_col, fontsize=12)
        ax.set_ylabel(y_col, fontsize=12)

        # 旋转x轴标签
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"小提琴图已保存到: {save_path}")

        return fig

    @error_handler("创建配对图")
    def create_pair_plot(self, df: pd.DataFrame, columns: List[str],
                        hue_col: Optional[str] = None, save_path: Optional[str] = None) -> plt.Figure:
        """
        创建配对图

        Args:
            df: 数据框
            columns: 要分析的列名列表
            hue_col: 分组列名
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        # 选择数据
        plot_data = df[columns + ([hue_col] if hue_col else [])].dropna()

        # 创建配对图
        g = sns.pairplot(plot_data, hue=hue_col, diag_kind='hist',
                        plot_kws={'alpha': 0.7}, diag_kws={'alpha': 0.7})

        # 设置标题
        g.fig.suptitle('Feature Pair Plot', fontsize=16, y=1.02)

        if save_path:
            g.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"配对图已保存到: {save_path}")

        return g.fig

    @error_handler("创建分布对比图")
    def create_distribution_comparison(self, df: pd.DataFrame, columns: List[str],
                                     target_col: Optional[str] = None,
                                     save_path: Optional[str] = None) -> plt.Figure:
        """
        创建分布对比图

        Args:
            df: 数据框
            columns: 要比较的列名列表
            target_col: 目标变量列名（用于分组）
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        n_cols = min(3, len(columns))
        n_rows = (len(columns) + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))

        # 确保axes是二维数组
        if len(columns) == 1:
            axes = np.array([[axes]])
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        elif n_cols == 1:
            axes = axes.reshape(-1, 1)

        for idx, col in enumerate(columns):
            row = idx // n_cols
            col_idx = idx % n_cols
            ax = axes[row, col_idx]

            if target_col and target_col in df.columns:
                # 按目标变量分组绘制
                for target_val in df[target_col].unique():
                    if pd.notna(target_val):
                        subset = df[df[target_col] == target_val][col].dropna()
                        if len(subset) > 0:
                            ax.hist(subset, alpha=0.7, label=f'{target_col}={target_val}',
                                   bins=30, density=True)
                ax.legend()
            else:
                # 单独绘制分布
                ax.hist(df[col].dropna(), bins=30, alpha=0.7, density=True)

            ax.set_title(f'{col} Distribution', fontsize=12)
            ax.set_xlabel(col)
            ax.set_ylabel('Density')
            ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for idx in range(len(columns), n_rows * n_cols):
            row = idx // n_cols
            col_idx = idx % n_cols
            if n_rows > 1:
                axes[row, col_idx].set_visible(False)
            else:
                axes[col_idx].set_visible(False)

        plt.suptitle('Distribution Comparison', fontsize=16, fontweight='bold')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"分布对比图已保存到: {save_path}")

        return fig

    def _perform_binning(self, X_var: pd.Series, y_var: pd.Series, n_bins: int,
                        binning_method: str, min_bin_size: int) -> Tuple[pd.Series, np.ndarray, int]:
        """
        执行分箱操作

        Args:
            X_var: 特征变量
            y_var: 目标变量
            n_bins: 请求的分箱数
            binning_method: 分箱方法
            min_bin_size: 最小分箱样本数

        Returns:
            tuple: (bins, bin_edges, actual_bins)
        """
        if binning_method == 'tree':
            return self._tree_binning(X_var, y_var, n_bins, min_bin_size)
        elif binning_method == 'chi2':
            return self._chi2_binning(X_var, y_var, n_bins, min_bin_size)
        elif binning_method == 'qcut':
            return self._qcut_binning(X_var, n_bins, min_bin_size)
        else:  # 'cut'
            return self._cut_binning(X_var, n_bins, min_bin_size)

    def _tree_binning(self, X_var: pd.Series, y_var: pd.Series, n_bins: int,
                     min_bin_size: int) -> Tuple[pd.Series, np.ndarray, int]:
        """决策树分箱"""
        try:
            from sklearn.tree import DecisionTreeClassifier

            # 创建决策树
            tree = DecisionTreeClassifier(
                max_leaf_nodes=n_bins,
                min_samples_leaf=min_bin_size,
                max_depth=4,
                class_weight='balanced',
                random_state=42
            )

            X_reshaped = X_var.values.reshape(-1, 1)
            tree.fit(X_reshaped, y_var)

            # 获取叶子节点的阈值
            thresholds = []
            def extract_thresholds(node_id=0):
                if tree.tree_.children_left[node_id] != tree.tree_.children_right[node_id]:
                    # 内部节点
                    threshold = tree.tree_.threshold[node_id]
                    thresholds.append(threshold)
                    extract_thresholds(tree.tree_.children_left[node_id])
                    extract_thresholds(tree.tree_.children_right[node_id])

            extract_thresholds()
            thresholds = sorted(list(set(thresholds)))

            if len(thresholds) == 0:
                # 回退到等频分箱
                return self._qcut_binning(X_var, n_bins, min_bin_size)

            # 创建分箱边界
            bin_edges = [-np.inf] + thresholds + [np.inf]
            bins = pd.cut(X_var, bins=bin_edges, duplicates='drop')

            return bins, np.array(bin_edges), len(bins.cat.categories)

        except Exception as e:
            self.logger.warning(f"决策树分箱失败: {e}，回退到等频分箱")
            return self._qcut_binning(X_var, n_bins, min_bin_size)

    def _chi2_binning(self, X_var: pd.Series, y_var: pd.Series, n_bins: int,
                     min_bin_size: int) -> Tuple[pd.Series, np.ndarray, int]:
        """卡方分箱"""
        try:
            # 初始等频分箱
            initial_bins = max(n_bins * 2, 10)  # 初始分箱数更多
            bins = pd.qcut(X_var, q=initial_bins, duplicates='drop')

            # 计算每个分箱的卡方值
            chi2_values = []
            for i in range(len(bins.cat.categories) - 1):
                # 获取相邻两个分箱的数据
                mask1 = bins == bins.cat.categories[i]
                mask2 = bins == bins.cat.categories[i + 1]

                if mask1.sum() == 0 or mask2.sum() == 0:
                    chi2_values.append(0)
                    continue

                # 构建2x2列联表
                y1 = y_var[mask1]
                y2 = y_var[mask2]

                table = np.array([
                    [y1.sum(), len(y1) - y1.sum()],
                    [y2.sum(), len(y2) - y2.sum()]
                ])

                # 计算卡方值
                from scipy.stats import chi2_contingency
                chi2, _, _, _ = chi2_contingency(table)
                chi2_values.append(chi2)

            # 合并卡方值最小的相邻分箱
            while len(bins.cat.categories) > n_bins and len(chi2_values) > 0:
                min_idx = np.argmin(chi2_values)
                # 这里简化处理，实际应该合并相邻分箱
                chi2_values.pop(min_idx)

            # 回退到等频分箱（简化实现）
            return self._qcut_binning(X_var, n_bins, min_bin_size)

        except Exception as e:
            self.logger.warning(f"卡方分箱失败: {e}，回退到等频分箱")
            return self._qcut_binning(X_var, n_bins, min_bin_size)

    def _qcut_binning(self, X_var: pd.Series, n_bins: int, min_bin_size: int) -> Tuple[pd.Series, np.ndarray, int]:
        """等频分箱"""
        try:
            bins = pd.qcut(X_var, q=n_bins, duplicates='drop')
            bin_edges = [-np.inf] + [interval.right for interval in bins.cat.categories]
            return bins, np.array(bin_edges), len(bins.cat.categories)
        except ValueError as e:
            self.logger.warning(f"等频分箱失败: {e}，回退到等距分箱")
            return self._cut_binning(X_var, n_bins, min_bin_size)

    def _cut_binning(self, X_var: pd.Series, n_bins: int, min_bin_size: int) -> Tuple[pd.Series, np.ndarray, int]:
        """等距分箱"""
        bins = pd.cut(X_var, bins=n_bins, duplicates='drop')
        bin_edges = [-np.inf] + [interval.right for interval in bins.cat.categories]
        return bins, np.array(bin_edges), len(bins.cat.categories)

    def _calculate_wilson_confidence_interval(self, positive_count: int, total_count: int,
                                            confidence: float = 0.95) -> Tuple[float, float]:
        """
        计算Wilson置信区间

        Args:
            positive_count: 正例数量
            total_count: 总数量
            confidence: 置信水平

        Returns:
            tuple: (下界, 上界)
        """
        if total_count == 0:
            return 0.0, 0.0

        p = positive_count / total_count
        n = total_count

        # 计算z值
        z = 1.96 if confidence == 0.95 else 2.576  # 95%或99%置信区间

        # Wilson置信区间公式
        denominator = 1 + z**2 / n
        center = (p + z**2 / (2 * n)) / denominator
        margin = z * np.sqrt((p * (1 - p) + z**2 / (4 * n)) / n) / denominator

        lower = max(0, center - margin)
        upper = min(1, center + margin)

        return lower, upper


# 全局实例
_data_explorer = None

def get_data_explorer() -> DataExplorer:
    """获取数据探索器实例"""
    global _data_explorer
    if _data_explorer is None:
        _data_explorer = DataExplorer()
    return _data_explorer
